<template>
  <el-dialog 
    :title="getDialogTitle()"
    :modelValue="dialogVisible"
    @update:modelValue="$emit('update:dialogVisible', $event)"
    width="80%"
    :before-close="handleClose"
    :close-on-click-modal="false"
    destroy-on-close
  >
    <el-form :model="kolForm" :rules="rules" ref="kolFormRef" label-width="200px">
      <el-tabs v-model="activeTab">
        <!-- Basic Info Tab -->
        <el-tab-pane label="基础信息" name="basic">
          <div class="form-section">
            <div class="section-title">基础信息</div>
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="达人名称" prop="kol_name" :disabled="dialogType === 'info'">
                  <el-input v-model="kolForm.kol_name" :disabled="dialogType === 'info'"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="达人ID" prop="platform_uid" :disabled="dialogType === 'info'">
                  <el-input v-model="kolForm.platform_uid" :disabled="dialogType === 'info'"></el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 抖音招募任务专用字段 -->
            <el-row :gutter="24" v-if="isDouyinRecruitmentTask">
              <el-col :span="12">
                <el-form-item label="达人粉丝量" prop="kol_fans_num" :disabled="dialogType === 'info'">
                  <el-input v-model="kolForm.kol_fans_num" :disabled="dialogType === 'info'"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="是否为星晓计划达人" prop="is_xingxiao_kol" :disabled="dialogType === 'info'">
                  <el-select
                    v-model="kolForm.is_xingxiao_kol"
                    placeholder="请选择"
                    style="width: 100%"
                    :disabled="dialogType === 'info'"
                  >
                    <el-option label="是" value="是"></el-option>
                    <el-option label="否" value="否"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="24">
              <!-- 合作形式字段在抖音招募模式下隐藏 -->
              <el-col :span="12" v-if="!isDouyinRecruitmentTask">
                <el-form-item label="合作形式" prop="cooperation_type" :disabled="dialogType === 'info'">
                  <el-select
                    v-model="kolForm.cooperation_type"
                    placeholder="请选择合作形式"
                    style="width: 100%"
                    :disabled="dialogType === 'info'"
                  >
                    <el-option
                      v-for="item in placementListArr"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="MCN机构简称" :disabled="dialogType === 'info'">
                  <el-select
                    v-model="kolForm.mcnObject.mcn_short_name"
                    filterable
                    placeholder="请选择MCN机构"
                    @change="handleMcnChange"
                    style="width: 100%"
                    :disabled="dialogType === 'info'"
                  >
                    <el-option
                      v-for="item in mcnList"
                      :key="item.id"
                      :label="item.mcn_short_name"
                      :value="item">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              
              <!-- For Xiaohongshu and Tencent, show task name if needed -->
              <el-col :span="12" v-if="(isXiaohongshu || isTencentMutual) && [1, 2, 6].includes(props.platformType)">
                <el-form-item label="任务名称" prop="xingtu_task_name" :disabled="dialogType === 'info'">
                  <el-input 
                    v-model="kolForm.xingtu_task_name" 
                    placeholder="请输入任务名称"
                    :disabled="dialogType === 'info'"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="联系方式" :disabled="dialogType === 'info'">
                  <el-input 
                    v-model="kolForm.contact_information" 
                    placeholder="请输入联系方式"
                    :disabled="dialogType === 'info'"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            
            <!-- Third row - media and MCN -->
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="建联媒介" :disabled="dialogType === 'info'">
                  <el-select 
                    v-model="kolForm.alliance_personnel" 
                    placeholder="请选择建联媒介" 
                    filterable 
                    remote 
                    style="width: 100%"
                    :disabled="dialogType === 'info'"
                    @change="handleMediumChange"
                  >
                    <el-option 
                      v-for="item in mediumOptions" 
                      :key="item.id" 
                      :label="item.name" 
                      :value="item.name">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            
            <!-- Fourth row - Contact information and Task/Project name -->
            <el-row :gutter="24">
              <!-- For non-Xiaohongshu platforms, show task name -->
              <el-col :span="12" v-if="props.platformType !== 3 && (!isXiaohongshu && !isTencentMutual)">
                <el-form-item label="任务名称" prop="xingtu_task_name" :disabled="dialogType === 'info'">
                  <el-input 
                    v-model="kolForm.xingtu_task_name" 
                    placeholder="请输入任务名称"
                    :disabled="dialogType === 'info'"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="24" v-if="props.platformType === 2">
              <el-col :span="12">
                <el-form-item label="项目名称" prop="xingtu_project_name" :disabled="dialogType === 'info'">
                  <el-input 
                    v-model="kolForm.xingtu_project_name" 
                    placeholder="请输入项目名称"
                    :disabled="dialogType === 'info'"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-tab-pane>

        <!-- Recruitment Talent Info Tab - Only for Douyin Recruitment Tasks -->
        <el-tab-pane v-if="isDouyinRecruitmentTask" label="利润信息" name="recruitment">
          <div class="form-section">
            <div class="section-title">利润信息</div>



            <!-- First row: 达人裸价（不含服务费）, 达人授权费 -->
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="达人裸价（不含服务费）" prop="kol_price" :disabled="dialogType === 'info'">
                  <el-input
                    v-model="kolForm.kol_price"
                    type="number"
                    :min="0"
                    :disabled="dialogType === 'info'"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="达人授权费" prop="kol_licensing_fee" :disabled="dialogType === 'info'">
                  <el-input
                    v-model="kolForm.kol_licensing_fee"
                    type="number"
                    :min="0"
                    :disabled="dialogType === 'info'"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <!-- Second row: 对客显示含服务费报价, 是否改价下单 -->
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="对客显示含服务费报价" prop="show_customer_fee" :disabled="dialogType === 'info'">
                  <el-input
                    v-model="kolForm.show_customer_fee"
                    type="number"
                    :min="0"
                    :disabled="dialogType === 'info'"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="是否改价下单" prop="change_status" :disabled="dialogType === 'info'">
                  <el-radio-group
                    v-model="kolForm.change_status"
                    :disabled="dialogType === 'info'"
                  >
                    <el-radio :label="1">是</el-radio>
                    <el-radio :label="0">否</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>

            <!-- Third row: 服务商下单价（不含服务费）, 服务商下单价（含服务费） -->
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="服务商下单价（不含服务费）" prop="provider_price_exclue_service_fee" :disabled="dialogType === 'info'">
                  <el-input
                    v-model="kolForm.provider_price_exclue_service_fee"
                    type="number"
                    :min="0"
                    :disabled="dialogType === 'info'"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="服务商下单价（含服务费）" prop="provider_price" :disabled="dialogType === 'info'">
                  <el-input
                    v-model="kolForm.provider_price"
                    type="number"
                    :min="0"
                    :disabled="dialogType === 'info'"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <!-- Fourth row: 期望发布日期, 预估应收媒体返点比例 -->
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="期望发布日期" prop="expect_release_time" :disabled="dialogType === 'info'">
                  <el-date-picker
                    v-model="kolForm.expect_release_time"
                    type="month"
                    placeholder="选择月份"
                    format="YYYY/MM"
                    value-format="YYYY-MM"
                    :disabled="dialogType === 'info'"
                    class="w-full"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="预估应收媒体返点比例" prop="predict_receivable_medium_ratio" :disabled="dialogType === 'info'">
                  <el-input
                    v-model="kolForm.predict_receivable_medium_ratio"
                    type="number"
                    :min="0"
                    :disabled="dialogType === 'info'"
                  >
                    <template #append> % </template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <!-- Fifth row: 客户返点比例, 客户返佣 -->
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="客户返点比例" prop="customer_rebate_ratio" :disabled="dialogType === 'info'">
                  <el-input
                    v-model="kolForm.customer_rebate_ratio"
                    type="number"
                    :min="0"
                    :disabled="dialogType === 'info'"
                  >
                    <template #append> % </template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="客户返佣" prop="customer_rebate" :disabled="dialogType === 'info'">
                  <el-input
                    v-model="kolForm.customer_rebate"
                    type="number"
                    :min="0"
                    :disabled="dialogType === 'info'"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <!-- Sixth row: 客户服务费 -->
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="客户服务费" prop="customer_service_price" :disabled="dialogType === 'info'">
                  <el-input
                    v-model="kolForm.customer_service_price"
                    type="number"
                    :min="0"
                    :disabled="dialogType === 'info'"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="预估应收客户款" prop="predict_receivable_customer_price_v1" required :disabled="dialogType === 'info'">
                  <el-input
                    v-model="kolForm.predict_receivable_customer_price_v1"
                    type="number"
                    :min="0"
                    :disabled="dialogType === 'info'"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 用户输入字段 - 预估应收客户款、毛利、毛利率 -->
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="毛利" prop="gross_profit_v1" required :disabled="dialogType === 'info'">
                  <el-input
                    v-model="kolForm.gross_profit_v1"
                    type="number"
                    :min="0"
                    :disabled="dialogType === 'info'"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="毛利率" prop="gross_profit_margin_v1" required :disabled="dialogType === 'info'">
                  <el-input
                    v-model="kolForm.gross_profit_margin_v1"
                    type="number"
                    :min="0"
                    :disabled="dialogType === 'info'"
                  >
                    <template #append> % </template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-tab-pane>

        <!-- Profit Tab -->
        <el-tab-pane v-if="!isDouyinRecruitmentTask" label="利润信息" name="profit">
          <div class="form-section">
            <div class="section-title">利润信息</div>
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="平台刊例价" prop="process_info.kol_base_price" :disabled="isProfitFieldDisabled">
                  <el-input
                    class="w-full"
                    type="number"
                    :min="0"
                    v-model="kolForm.process_info.kol_base_price"
                    :disabled="pageType === 'info' || (!isXiaohongshu && !isTencentMutual && !isKuaishou && !isBilibili)"
                    @change="handleBasePriceChange"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="达人授权费(不含服务费)" prop="process_info.kol_licensing_fee" :disabled="isProfitFieldDisabled">
                  <el-input
                    class="w-full"
                    type="number"
                    :min="0"
                    v-model="kolForm.process_info.kol_licensing_fee"
                    @blur="amountPriceCalculate"
                    :disabled="isProfitFieldDisabled"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="24">
              
              <el-col :span="12" v-if="isBilibili">
                <el-form-item label="服务类型" prop="process_info.bili_service_type" :disabled="isProfitFieldDisabled">
                  <el-select
                    v-model="kolForm.process_info.bili_service_type"
                    placeholder="请选择服务类型"
                    :disabled="isProfitFieldDisabled"
                    class="w-full"
                  >
                    <el-option label="个人UP主服务费 (任务金额*7%)" :value="1" />
                    <el-option label="签约UP主服务费 (任务金额*5%)" :value="2" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12" :class="{'col-start': !isTencentMutual}">
                <el-form-item label="是否改价" :disabled="dialogType === 'info'">
                  <el-radio-group 
                    v-model="kolForm.process_info.change_status" 
                    @change="radioChange" 
                    :disabled="dialogType === 'info'"
                  >
                    <el-radio :label="1">是</el-radio>
                    <el-radio :label="0">否</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="下单总金额" prop="process_info.star_price" :disabled="dialogType === 'info'">
                  <div class="flex items-center" style="width: 100%">
                    <el-tooltip effect="dark" :content="isBilibili ? '下单总金额=(达人裸价+达人授权费裸价)*(个人UP主：7%服务费，签约UP主：5%服务费)' : isXiaohongshu ? '下单总金额=(达人裸价+达人授权费裸价)*1.1' : '星图价格=(达人星图裸价+达人授权费裸价)*1.05'" placement="top">
                      <el-icon color="#333333" class="no-inherit"><InfoFilled /></el-icon>
                    </el-tooltip>
                    <el-input 
                      class="w-full" 
                      type="number" 
                      :min="0" 
                      v-model="star_price" 
                      :disabled="true" 
                    />
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="实际下单价格（含服务费）" prop="process_info.actual_amount_price" :disabled="isProfitFieldDisabled">
                  <el-input
                    class="w-full"
                    type="number"
                    :min="0"
                    v-model="kolForm.process_info.actual_amount_price"
                    :disabled="dialogType === 'info' || (props.orderMethod === 1 && kolForm.process_info.change_status !== 1)"
                    @blur="formatActualAmount"
                  />
                </el-form-item>
              </el-col>
              
            </el-row>
            
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="原返点比例" prop="process_info.original_rebate_ratio" :disabled="dialogType === 'info'">
                  <el-input
                    class="w-full"
                    type="number"
                    :min="0"
                    v-model="kolForm.process_info.original_rebate_ratio"
                    :disabled="true"
                  >
                    <template #append> % </template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="预估应收媒体返点比例(本次)" prop="process_info.predict_receivable_medium_ratio" :disabled="isProfitFieldDisabled">
                  <el-input
                    class="w-full"
                    type="number"
                    :min="0"
                    v-model="kolForm.process_info.predict_receivable_medium_ratio"
                    @change="changeMediumRatio"
                    :disabled="isProfitFieldDisabled"
                  >
                    <template #append> % </template>
                  </el-input>
                </el-form-item>
              </el-col>
              
            </el-row>
            
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="预估应收媒体返点金额(本次)" prop="process_info.predict_receivable_medium_price" :disabled="isProfitFieldDisabled">
                  <el-input
                    class="w-full"
                    type="number"
                    :min="0"
                    v-model="kolForm.process_info.predict_receivable_medium_price"
                    @input="handleMediumPriceInput"
                    @blur="handleMediumPriceBlur"
                    :disabled="isProfitFieldDisabled"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="预估成本" prop="process_info.predict_cost" :disabled="dialogType === 'info'">
                  <div class="flex items-center" style="width: 100%">
                    <el-tooltip effect="dark" content="预估成本=实际下单价格-预估应收媒体返点" placement="top">
                      <el-icon color="#333333" class="no-inherit"><InfoFilled /></el-icon>
                    </el-tooltip>
                    <el-input 
                      class="w-full" 
                      type="number" 
                      :min="0" 
                      v-model="predict_cost" 
                      :disabled="true" 
                    />
                  </div>
                </el-form-item>
              </el-col>
              
            </el-row>
            
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="客户返佣" prop="process_info.customer_rebate" :disabled="isProfitFieldDisabled">
                  <el-input
                    class="w-full"
                    type="number"
                    :min="0"
                    v-model="kolForm.process_info.customer_rebate"
                    :disabled="isProfitFieldDisabled"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="客户服务费" prop="process_info.customer_service_price" :disabled="isProfitFieldDisabled">
                  <el-input
                    class="w-full"
                    type="number"
                    :min="0"
                    v-model="kolForm.process_info.customer_service_price"
                    :disabled="isProfitFieldDisabled"
                  />
                </el-form-item>
              </el-col>
              
            </el-row>
            
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="预估应收客户款" prop="process_info.predict_receivable_customer_price" :disabled="dialogType === 'info'">
                  <div class="flex items-center" style="width: 100%">
                    <el-tooltip effect="dark" content="预估应收客户款=实际下单价格-客户返佣+客户服务费" placement="top">
                      <el-icon color="#333333" class="no-inherit"><InfoFilled /></el-icon>
                    </el-tooltip>
                    <el-input
                      class="w-full"
                      type="number"
                      :min="0"
                      v-model="predict_receivable_customer_price"
                      :disabled="true"
                    />
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="毛利" prop="process_info.gross_profit" :disabled="dialogType === 'info'">
                  <div class="flex items-center" style="width: 100%">
                    <el-tooltip effect="dark" content="毛利=预估应收客户款-预估成本" placement="top">
                      <el-icon color="#333333" class="no-inherit"><InfoFilled /></el-icon>
                    </el-tooltip>
                    <el-input 
                      class="w-full" 
                      type="number" 
                      :min="0" 
                      v-model="gross_profit" 
                      :disabled="true" 
                    />
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="毛利率" prop="process_info.gross_profit_margin" :disabled="dialogType === 'info'">
                  <div class="flex items-center" style="width: 100%">
                    <el-tooltip effect="dark" content="毛利率=毛利/预估应收客户款*100%" placement="top">
                      <el-icon color="#333333" class="no-inherit"><InfoFilled /></el-icon>
                    </el-tooltip>
                    <el-input 
                      class="w-full" 
                      type="number" 
                      :min="0" 
                      v-model="gross_profit_margin" 
                      :disabled="true"
                    >
                      <template #append> % </template>
                    </el-input>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="特殊返点" :disabled="isProfitFieldDisabled">
                  <el-input
                    class="w-full"
                    type="text"
                    v-model="kolForm.process_info.special_rebate_amount"
                    :disabled="isProfitFieldDisabled"
                  />
                </el-form-item>
              </el-col>
              
            </el-row>
            
            <el-row :gutter="24">
              <el-col :span="24">
                <el-form-item label="备注" :disabled="isProfitFieldDisabled">
                  <el-input
                    type="textarea"
                    v-model="kolForm.process_info.other_notes"
                    rows="4"
                    :disabled="isProfitFieldDisabled"
                    style="width: 100%;"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="24">
              <el-col :span="24">
                <el-form-item label="附件" :disabled="isProfitFieldDisabled">
                  <el-upload
                    :disabled="isProfitFieldDisabled"
                    :http-request="uploadRequest"
                    v-model:file-list="fileList"
                    list-type="picture-card"
                    @preview="handlePictureCardPreview"
                    @remove="handleRemove"
                  >
                    <el-icon><Plus /></el-icon>
                  </el-upload>
                  <el-dialog v-model="dialogImageVisible">
                    <img w-full :src="dialogImageUrl" alt="Preview Image" />
                  </el-dialog>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-tab-pane>
        
        <!-- Order Info Tab for Douyin -->
        <el-tab-pane v-if="showDouyinOrderInfo" label="下单信息" name="douyin">
          <div class="form-section">
            <div class="section-title">下单信息</div>
            
            
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="期望发布时间" prop="order_info.expect_release_time" required :disabled="dialogType === 'info'">
                  <el-date-picker
                    v-model="kolForm.order_info.expect_release_time"
                    type="date"
                    placeholder="选择日期"
                    format="YYYY/MM/DD"
                    value-format="YYYY-MM-DD"
                    :disabled="dialogType === 'info'"
                    class="w-full"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="期望保留时长" prop="order_info.expect_save_days" required :disabled="dialogType === 'info'">
                  <div class="flex items-center">
                    <el-input
                      v-model="kolForm.order_info.expect_save_days"
                      type="number"
                      :min="0"
                      :disabled="dialogType === 'info'"
                      class="w-full"
                    />
                    <span class="ml-2">天</span>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="24">
              <el-col :span="24">
                <el-form-item label="是否投放广告" prop="order_info.is_publish_adv" :disabled="dialogType === 'info'">
                  <el-checkbox-group v-model="selectedAdvOptions" @change="handleAdvOptionsChange" :disabled="dialogType === 'info'">
                    <el-checkbox label="1">投放效果广告</el-checkbox>
                    <el-checkbox label="2">投放品牌广告</el-checkbox>
                    <el-checkbox label="3">投放巨量千川</el-checkbox>
                    <el-checkbox label="4">投放至Dou+</el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item 
                  label="巨量广告主ID-效果广告" 
                  prop="order_info.juliang_effect_adv_id" 
                  :disabled="dialogType === 'info'"
                  :required="selectedAdvOptions.includes('1')"
                >
                  <el-input
                    v-model="kolForm.order_info.juliang_effect_adv_id"
                    :disabled="dialogType === 'info' || !selectedAdvOptions.includes('1')"
                    class="w-full"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item 
                  label="巨量广告主ID-品牌广告" 
                  prop="order_info.juliang_brand_adv_id" 
                  :disabled="dialogType === 'info'"
                  :required="selectedAdvOptions.includes('2')"
                >
                  <el-input
                    v-model="kolForm.order_info.juliang_brand_adv_id"
                    :disabled="dialogType === 'info' || !selectedAdvOptions.includes('2')"
                    class="w-full"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item 
                  label="巨量千川广告主ID" 
                  prop="order_info.juliang_qianchuan_adv_id" 
                  :disabled="dialogType === 'info'"
                  :required="selectedAdvOptions.includes('3')"
                >
                  <el-input
                    v-model="kolForm.order_info.juliang_qianchuan_adv_id"
                    :disabled="dialogType === 'info' || !selectedAdvOptions.includes('3')"
                    class="w-full"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item 
                  label="Dou+广告主ID" 
                  prop="order_info.dou_adv_id" 
                  :disabled="dialogType === 'info'"
                  :required="selectedAdvOptions.includes('4')"
                >
                  <el-input
                    v-model="kolForm.order_info.dou_adv_id"
                    :disabled="dialogType === 'info' || !selectedAdvOptions.includes('4')"
                    class="w-full"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item 
                  label="Dou+广告主抖音UID" 
                  prop="order_info.dou_adv_uid" 
                  :disabled="dialogType === 'info'"
                  :required="selectedAdvOptions.includes('4')"
                >
                  <el-input
                    v-model="kolForm.order_info.dou_adv_uid"
                    :disabled="dialogType === 'info' || !selectedAdvOptions.includes('4')"
                    class="w-full"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="组件名称" prop="order_info.component_type" :disabled="dialogType === 'info'">
                  <el-select
                    v-model="kolForm.order_info.component_type"
                    placeholder="请选择组件"
                    :disabled="dialogType === 'info'"
                    class="w-full"
                  >
                    <el-option label="购物车" :value="1" />
                    <el-option label="落地页" :value="2" />
                    <el-option label="搜索组件" :value="3" />
                    <el-option label="其他" :value="4" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="24">
              <el-col :span="24">
                <el-form-item label="组件文案" prop="order_info.component_content" :disabled="dialogType === 'info'">
                  <el-input
                    v-model="kolForm.order_info.component_content"
                    :disabled="dialogType === 'info'"
                    class="w-full"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="24">
              <el-col :span="24">
                <el-form-item label="组件链接" prop="order_info.component_url" :disabled="dialogType === 'info'">
                  <el-input
                    v-model="kolForm.order_info.component_url"
                    :disabled="dialogType === 'info'"
                    class="w-full"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="24">
              <el-col :span="24">
                <el-form-item label="组件备注" prop="order_info.component_remark" :disabled="dialogType === 'info'">
                  <el-input
                    type="textarea"
                    v-model="kolForm.order_info.component_remark"
                    rows="4"
                    :disabled="dialogType === 'info'"
                    class="w-full"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="24">
              <el-col :span="24">
                <el-form-item label="其他特殊备注（如有）" prop="order_info.extra_remark" :disabled="dialogType === 'info'">
                  <el-input
                    type="textarea"
                    v-model="kolForm.order_info.extra_remark"
                    rows="4"
                    :disabled="dialogType === 'info'"
                    class="w-full"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-tab-pane>
        
        <!-- Order Info Tab for Xiaohongshu -->
        <el-tab-pane v-if="isXiaohongshu" label="下单信息" name="xiaohongshu">
          <div class="form-section">
            <div class="section-title">下单信息</div>
            
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="类型" prop="order_info.ext.xhs_type" required :disabled="dialogType === 'info'">
                  <el-select
                    v-model="kolForm.order_info.ext.xhs_type"
                    placeholder="请选择类型"
                    :disabled="dialogType === 'info'"
                    class="w-full"
                  >
                    <el-option label="蒲公英" value="蒲公英" />
                    <el-option label="小红盟" value="小红盟" />
                    <el-option label="小红星" value="小红星" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="报备品牌名" prop="order_info.ext.xhs_brand_name" required :disabled="dialogType === 'info'">
                  <el-tooltip content="务必精准对应品牌专业号" placement="top">
                    <el-input
                      v-model="kolForm.order_info.ext.xhs_brand_name"
                      :disabled="dialogType === 'info'"
                      class="w-full"
                    />
                  </el-tooltip>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="产品名称" prop="order_info.ext.xhs_product_name" required :disabled="dialogType === 'info'">
                  <el-input
                    v-model="kolForm.order_info.ext.xhs_product_name"
                    :disabled="dialogType === 'info'"
                    class="w-full"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="商品ID" prop="order_info.ext.xhs_product_id" :disabled="dialogType === 'info'">
                  <el-tooltip content="投放小红星时填写" placement="top">
                    <el-input
                      v-model="kolForm.order_info.ext.xhs_product_id"
                      :disabled="dialogType === 'info'"
                      class="w-full"
                    />
                  </el-tooltip>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="发布日期" prop="order_info.ext.xhs_publish_date" required :disabled="dialogType === 'info'">
                  <el-date-picker
                    v-model="kolForm.order_info.ext.xhs_publish_date"
                    type="date"
                    placeholder="选择日期"
                    format="YYYY/MM/DD"
                    value-format="YYYY-MM-DD"
                    :disabled="dialogType === 'info'"
                    class="w-full"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="绑定SPU" prop="order_info.ext.xhs_bind_spu" :disabled="dialogType === 'info'">
                  <el-tooltip content="提供准确名称/SPUID" placement="top">
                    <el-input
                      v-model="kolForm.order_info.ext.xhs_bind_spu"
                      :disabled="dialogType === 'info'"
                      class="w-full"
                    />
                  </el-tooltip>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="24">
              <el-col :span="24">
                <el-form-item label="备注" prop="order_info.ext.xhs_ex_remark" :disabled="dialogType === 'info'">
                    <el-input
                      type="textarea"
                      v-model="kolForm.order_info.ext.xhs_ex_remark"
                      rows="4"
                      :disabled="dialogType === 'info'"
                      class="w-full"
                      placeholder="1、改价下单比例 2、是否要求勾选广审 3、是否是合集 4、是否有特殊要求如组件等"
                    />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-tab-pane>
        
        <!-- Order Info Tab for Tencent -->
        <el-tab-pane v-if="isTencentMutual" label="下单信息" name="tencent">
          <div class="form-section">
            <div class="section-title">下单信息</div>
            
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="是否为如翼R0选号达人" prop="order_info.ext.tencent_is_ry_talent" required :disabled="dialogType === 'info'">
                  <el-select
                    v-model="kolForm.order_info.ext.tencent_is_ry_talent"
                    placeholder="请选择"
                    :disabled="dialogType === 'info'"
                    class="w-full"
                  >
                    <el-option label="是" value="是" />
                    <el-option label="否" value="否" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="期望发布时间" prop="order_info.ext.tencent_expect_pb_time" required :disabled="dialogType === 'info'">
                  <el-tooltip content="注：如果在任务结束提起之前达人未发布，则订单自动取消）" placement="top">
                    <el-date-picker
                      v-model="kolForm.order_info.ext.tencent_expect_pb_time"
                      type="date"
                      placeholder="选择日期"
                      format="YYYY/MM/DD"
                      value-format="YYYY-MM-DD"
                      :disabled="dialogType === 'info'"
                      class="w-full"
                    />
                  </el-tooltip>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="推广产品" prop="order_info.ext.tencent_product_name" required :disabled="dialogType === 'info'">
                  <el-input
                    v-model="kolForm.order_info.ext.tencent_product_name"
                    :disabled="dialogType === 'info'"
                    class="w-full"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="产品介绍" prop="order_info.ext.tencent_product_desc" required :disabled="dialogType === 'info'">
                  <el-input
                    v-model="kolForm.order_info.ext.tencent_product_desc"
                    :disabled="dialogType === 'info'"
                    class="w-full"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="24">
              <el-col :span="24">
                <el-form-item label="视频内容要求" prop="order_info.ext.tencent_video_rquirement" :disabled="dialogType === 'info'">
                  <el-input
                    type="textarea"
                    v-model="kolForm.order_info.ext.tencent_video_rquirement"
                    rows="3"
                    :disabled="dialogType === 'info'"
                    class="w-full"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="24">
              <el-col :span="24">
                <el-form-item label="外层视频描述文案" prop="order_info.ext.tencent_outside_video_desc" :disabled="dialogType === 'info'">
                  <el-input
                    type="textarea"
                    v-model="kolForm.order_info.ext.tencent_outside_video_desc"
                    rows="3"
                    :disabled="dialogType === 'info'"
                    class="w-full"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="是否进行脚本确认" prop="order_info.ext.tencent_script_confirm" :disabled="dialogType === 'info'">
                  <el-select
                    v-model="kolForm.order_info.ext.tencent_script_confirm"
                    placeholder="请选择"
                    :disabled="dialogType === 'info'"
                    class="w-full"
                  >
                    <el-option label="是" value="是" />
                    <el-option label="否" value="否" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="是否启用广告组件" prop="order_info.ext.tencent_enable_adv_component" :disabled="dialogType === 'info'">
                  <el-select
                    v-model="kolForm.order_info.ext.tencent_enable_adv_component"
                    placeholder="请选择"
                    :disabled="dialogType === 'info'"
                    class="w-full"
                  >
                    <el-option label="是" value="是" />
                    <el-option label="否" value="否" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="24">
              <el-col :span="24">
                <el-form-item label="推广场景" prop="order_info.ext.tencent_promotion_scene" :disabled="dialogType === 'info'">
                  <el-select
                    v-model="kolForm.order_info.ext.tencent_promotion_scene"
                    placeholder="请选择"
                    :disabled="dialogType === 'info'"
                    class="w-full"
                  >
                    <el-option label="推广品牌活动" value="推广品牌活动" />
                    <el-option label="添加商家微信" value="添加商家微信" />
                    <el-option label="推广小游戏" value="推广小游戏" />
                    <el-option label="收集销售线索" value="收集销售线索" />
                    <el-option label="关注公众号" value="关注公众号" />
                    <el-option label="关注视频号" value="关注视频号" />
                    <el-option label="视频号直播预约" value="视频号直播预约" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
           
            <!-- 新增字段: 营销目标 -->
            <el-row :gutter="24">
              <el-col :span="24">
                <el-form-item label="营销目标" prop="order_info.ext.tencent_market_target" :disabled="dialogType === 'info'">
                  <el-select
                    v-model="kolForm.order_info.ext.tencent_market_target"
                    placeholder="请选择营销目标"
                    :disabled="dialogType === 'info'"
                    class="w-full"
                  >
                    <el-option label="品牌曝光" :value="1" />
                    <el-option label="破圈种草" :value="2" />
                    <el-option label="行动转化" :value="3" />
                    <el-option label="商品推广" :value="4" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            
            <!-- 新增字段: 推广目标 -->
            <el-row :gutter="24">
              <el-col :span="24">
                <el-form-item label="推广目标" prop="order_info.ext.tencent_promote_target" required :disabled="dialogType === 'info'">
                  <el-select
                    v-model="kolForm.order_info.ext.tencent_promote_target"
                    placeholder="请选择推广目标"
                    :disabled="dialogType === 'info'"
                    class="w-full"
                  >
                    <el-option label="推广品牌活动" :value="1" />
                    <el-option label="推广小店商品" :value="2" />
                    <el-option label="推广应用" :value="3" />
                    <el-option label="添加商家微信" :value="4" />
                    <el-option label="推广小游戏" :value="5" />
                    <el-option label="收集销售线索" :value="6" />
                    <el-option label="关注公众号" :value="7" />
                    <el-option label="关注视频号" :value="8" />
                    <el-option label="视频号直播预约" :value="9" />
                    <el-option label="联名款红包封面" :value="10" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="24">
              <el-col :span="24">
                <el-form-item label="组件信息" prop="order_info.ext.tencent_component_info" :disabled="dialogType === 'info'">
                  <el-input
                    type="textarea"
                    v-model="kolForm.order_info.ext.tencent_component_info"
                    rows="3"
                    :disabled="dialogType === 'info'"
                    class="w-full"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="24">
              <el-col :span="24">
                <el-form-item label="是否有二次推广意向" prop="order_info.ext.tencent_enable_second_promotion" :disabled="dialogType === 'info'">
                  <el-tooltip content="如果是，请提供需要授权的视频号名称" placement="top">
                    <el-input
                      type="textarea"
                      v-model="kolForm.order_info.ext.tencent_enable_second_promotion"
                      rows="2"
                      :disabled="dialogType === 'info'"
                      class="w-full"
                    />
                  </el-tooltip>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-tab-pane>

        <!-- Order Info Tab for Kuaishou - UPDATED -->
        <el-tab-pane v-if="isKuaishou" label="下单信息" name="kuaishou">
          <div class="form-section">
            <div class="section-title">下单信息</div>
            
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="期望发布日期" prop="order_info.expect_release_time" required :disabled="dialogType === 'info'">
                  <el-date-picker
                    v-model="kolForm.order_info.expect_release_time"
                    type="date"
                    placeholder="选择日期"
                    format="YYYY/MM/DD"
                    value-format="YYYY-MM-DD"
                    :disabled="dialogType === 'info'"
                    class="w-full"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="期望保留时长" prop="order_info.expect_save_days" required :disabled="dialogType === 'info'">
                  <div class="flex items-center">
                    <el-input
                      v-model="kolForm.order_info.expect_save_days"
                      type="number"
                      :min="0"
                      :disabled="dialogType === 'info'"
                      class="w-full"
                    />
                    <span class="ml-2">天</span>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="24">
              <el-col :span="24">
                <el-form-item label="是否投放广告" prop="order_info.is_publish_adv" :disabled="dialogType === 'info'">
                  <el-checkbox-group v-model="kuaishouAdvOptions" @change="handleKuaishouAdvOptionsChange" :disabled="dialogType === 'info'">
                    <el-checkbox label="1">磁力智投</el-checkbox>
                    <el-checkbox label="2">磁力金牛</el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item 
                  label="磁力智投账号ID" 
                  prop="order_info.dou_adv_id" 
                  :disabled="dialogType === 'info'"
                  :required="kuaishouAdvOptions.includes('1')"
                >
                  <el-input
                    v-model="kolForm.order_info.dou_adv_id"
                    :disabled="dialogType === 'info' || !kuaishouAdvOptions.includes('1')"
                    class="w-full"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item 
                  label="磁力金牛账号ID" 
                  prop="order_info.dou_adv_uid" 
                  :disabled="dialogType === 'info'"
                  :required="kuaishouAdvOptions.includes('2')"
                >
                  <el-input
                    v-model="kolForm.order_info.dou_adv_uid"
                    :disabled="dialogType === 'info' || !kuaishouAdvOptions.includes('2')"
                    class="w-full"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="组件类型" prop="order_info.component_type" :disabled="dialogType === 'info'">
                  <el-select
                    v-model="kolForm.order_info.component_type"
                    placeholder="请选择组件类型"
                    :disabled="dialogType === 'info'"
                    class="w-full"
                  >
                    <el-option label="应用推广" :value="1" />
                    <el-option label="品牌推广" :value="2" />
                    <el-option label="快分销带货" :value="3" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="24">
              <el-col :span="24">
                <el-form-item label="组件名称/信息" prop="order_info.component_content" :disabled="dialogType === 'info'">
                  <el-input
                    v-model="kolForm.order_info.component_content"
                    :disabled="dialogType === 'info'"
                    class="w-full"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="24">
              <el-col :span="24">
                <el-form-item label="组件-链接" prop="order_info.component_url" :disabled="dialogType === 'info'">
                  <el-input
                    v-model="kolForm.order_info.component_url"
                    :disabled="dialogType === 'info'"
                    class="w-full"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="24">
              <el-col :span="24">
                <el-form-item label="组件-备注" prop="order_info.component_remark" :disabled="dialogType === 'info'">
                  <el-input
                    type="textarea"
                    v-model="kolForm.order_info.component_remark"
                    rows="4"
                    :disabled="dialogType === 'info'"
                    class="w-full"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="24">
              <el-col :span="24">
                <el-form-item label="其他特殊备注（如有）" prop="order_info.extra_remark" :disabled="dialogType === 'info'">
                  <el-input
                    type="textarea"
                    v-model="kolForm.order_info.extra_remark"
                    rows="4"
                    :disabled="dialogType === 'info'"
                    class="w-full"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-tab-pane>
        
        <!-- Order Info Tab for Bilibili -->
        <el-tab-pane v-if="isBilibili" label="下单信息" name="bilibili">
          <div class="form-section">
            <div class="section-title">下单信息</div>

            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="期望发布时间" prop="order_info.expect_release_time" required :disabled="dialogType === 'info'">
                  <el-date-picker
                    v-model="kolForm.order_info.expect_release_time"
                    type="date"
                    placeholder="选择日期"
                    format="YYYY/MM/DD"
                    value-format="YYYY-MM-DD"
                    :disabled="dialogType === 'info'"
                    class="w-full"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="联系方式" prop="order_info.ext.bili_contact_information" required :disabled="dialogType === 'info'">
                  <el-input
                    v-model="kolForm.order_info.ext.bili_contact_information"
                    placeholder="请输入联系方式"
                    :disabled="dialogType === 'info'"
                    class="w-full"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="产品名称" prop="order_info.ext.bili_product_name" required :disabled="dialogType === 'info'">
                  <el-input
                    v-model="kolForm.order_info.ext.bili_product_name"
                    placeholder="请输入产品名称"
                    :disabled="dialogType === 'info'"
                    class="w-full"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="24">
              <el-col :span="24">
                <el-form-item label="制作要求" prop="order_info.ext.bili_product_requirement" :disabled="dialogType === 'info'">
                  <el-input
                    type="textarea"
                    v-model="kolForm.order_info.ext.bili_product_requirement"
                    rows="4"
                    placeholder="请输入制作要求"
                    :disabled="dialogType === 'info'"
                    class="w-full"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="24">
              <el-col :span="24">
                <el-form-item label="备注" prop="order_info.ext.bili_notes" :disabled="dialogType === 'info'">
                  <el-input
                    type="textarea"
                    v-model="kolForm.order_info.ext.bili_notes"
                    rows="4"
                    placeholder="请输入备注"
                    :disabled="dialogType === 'info'"
                    class="w-full"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-form>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="submitForm" v-if="dialogType !== 'info'">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, nextTick, onMounted } from 'vue';
import { uploadMcnAnnex } from "@/api/modules/kol";
import { ElMessage } from "element-plus";
import { Plus, InfoFilled } from '@element-plus/icons-vue';

const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false
  },
  dialogType: {
    type: String,
    default: 'add' // 'add', 'edit', 'info'
  },
  mediumOptions: {
    type: Array,
    default: () => []
  },
  mcnList: {
    type: Array,
    default: () => []
  },
  platformType: {
    type: Number,
    default: 1 // 默认为抖音平台
  },
  taskType: {
    type: Number,
    default: 1 
  },
  orderMethod: {
    type: Number,
    default: 1 // 走星图下单为1，直接下单为2
  },
  orderType: {
    type: Number,
    default: 1 
  },
  rowData: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:dialogVisible', 'cancel', 'confirm']);

// Dialog title based on dialog type
const getDialogTitle = () => {
  if (props.dialogType === 'add') return '添加达人';
  if (props.dialogType === 'edit') return '编辑达人信息';
  return '查看达人信息';
};

// Form reference and state variables
const kolFormRef = ref(null);
const activeTab = ref('basic'); // Default active tab is basic info
const fileList = ref([]);
const dialogImageVisible = ref(false);
const dialogImageUrl = ref("");
const selectedAdvOptions = ref([]); // Array to hold checkbox values for is_publish_adv
const kuaishouAdvOptions = ref([]); // Array to hold checkbox values for Kuaishou ads

// Initialize the complete form structure
const kolForm = ref({
  // Basic KOL information
  kol_name: '',
  platform_uid: '',
  cooperation_type: null, // Changed from 1 to null
  xingtu_task_name: '',
  xingtu_project_name: '', // Add the new field for platformType 2
  kol_price: 0,
  alliance_personnel: '',
  alliance_personnel_id: '',
  mcnObject: {
    mcn_name: '',
    mcn_short_name: '',
    mcn_id: ''
  },
  mcn_name: '',
  mcn_short_name: '',
  mcn_id: '',
  contact_information: '',
  kol_num: 1,
  price_1_20: 0,
  price_20_60: 0,
  price_60: 0,
  rebate_ratio: 0,
  kol_attributes: 1, // 1 野生达人，2 机构达人
  kol_type: 1,
  collection_status: 1,

  // Recruitment task specific fields (for Douyin recruitment mode)
  kol_fans_num: '',
  is_xingxiao_kol: '',
  kol_licensing_fee: 0,
  show_customer_fee: 0,
  change_status: '',
  provider_price_exclue_service_fee: 0,
  provider_price: 0,
  expect_release_time: '',
  predict_receivable_medium_ratio: 0,
  customer_rebate_ratio: 0,
  customer_rebate: 0,
  customer_service_price: 0,

  // v1 fields for display/echo (backend returned values)
  predict_receivable_customer_price_v1: 0,
  gross_profit_v1: 0,
  gross_profit_margin_v1: 0,

  // Process information (profit-related data)
  process_info: {
    kol_base_price: 0, // Synchronized with kol_price
    kol_licensing_fee: 0,
    change_status: 0,
    actual_amount_price: 0,
    predict_receivable_medium_price: 0,
    predict_receivable_medium_ratio: 0,
    customer_rebate: 0,
    customer_service_price: 0,
    special_rebate_amount: '',
    other_notes: '',
    gross_profit: 0,
    gross_profit_margin: 0,
    original_rebate_ratio: 0,
    star_price: 0, // StarGraph price
    predict_cost: 0,
    predict_receivable_customer_price: 0,
    img_url: '',
    bili_service_type: 1 // Bilibili service type: 1 for 个人UP主服务费, 2 for 签约UP主服务费
  },
  
  // Order information
  order_info: {
    expect_release_time: '',
    expect_save_days: '',
    juliang_effect_adv_id: '',
    juliang_brand_adv_id: '',
    juliang_qianchuan_adv_id: '',
    dou_adv_id: '',
    dou_adv_uid: '',
    component_type: '',
    component_content: '',
    component_url: '',
    component_remark: '',
    extra_remark: '',
    is_publish_adv: '',
    // Platform-specific fields inside ext object
    ext: {
      // Xiaohongshu fields
      xhs_type: '',
      xhs_brand_name: '',
      xhs_product_name: '',
      xhs_order_total_price: 0,
      xhs_publish_date: '',
      xhs_ex_remark: '',
      xhs_bind_spu: '',
      xhs_product_id: '',
      
      // Tencent fields
      tencent_number: '',
      tencent_is_ry_talent: '',
      tencent_expect_pb_time: '',
      tencent_order_total_price: 0,
      tencent_product_name: '',
      tencent_product_desc: '',
      tencent_video_rquirement: '',
      tencent_outside_video_desc: '',
      tencent_script_confirm: '',
      tencent_promotion_scene: '',
      tencent_enable_adv_component: '',
      tencent_component_info: '',
      tencent_enable_second_promotion: '',
      tencent_market_target: null, // 营销目标
      tencent_promote_target: null, // 推广目标
      
      // Bilibili fields
      bili_service_type: 1, // Default to "个人UP主服务费"
      bili_product_name: '',
      bili_contact_information: '',
      bili_product_requirement: '',
      bili_notes: ''
    }
  }
});

// Computed properties to determine which tabs to show
const isXiaohongshu = computed(() => props.platformType === 3);
const isTencentMutual = computed(() => props.platformType === 6);
const isKuaishou = computed(() => props.platformType === 4);
const isBilibili = computed(() => props.platformType === 5);
const isDouyinRecruitmentTask = computed(() => {
  const isDouyinPlatform = props.platformType === 1 || props.platformType === 2;
  const isRecruitmentTask = props.taskType === 2; // 招募任务模式
  return isDouyinPlatform && isRecruitmentTask;
});
const showDouyinOrderInfo = computed(() => {
  const isDouyinPlatform = props.platformType === 1 || props.platformType === 2;
  const isDirectOrder = props.orderMethod === 2;
  const isRecruitmentTask = props.taskType === 2; // 招募任务模式
  // 抖音平台且直接下单且非招募任务模式时才显示下单信息tab
  return isDouyinPlatform && isDirectOrder && !isRecruitmentTask;
});

const isProfitFieldDisabled = computed(() => {
  return props.dialogType === 'info' || props.orderType === 2;
});

const bilibiliCooperationTypes = computed(() => {
  const taskType = props.taskType || 1;

  if (taskType === 1) {
    return [
      { label: "定制视频", value: 1 },
      { label: "植入视频", value: 2 }
    ];
  } else if (taskType === 2) {
    return [
      { label: "定制视频", value: 1 },
      { label: "植入视频", value: 2 },
      { label: "直发动态", value: 3 },
      { label: "转发动态", value: 4 },
      { label: "线下活动", value: 5 },
      { label: "参与拍摄", value: 6 },
      { label: "代投", value: 7 },
      { label: "创作授权", value: 8 },
      { label: "画师手绘", value: 9 },
      { label: "其他", value: 10 }
    ];
  }

  // Default fallback
  return [
    { label: "定制视频", value: 1 },
    { label: "植入视频", value: 2 }
  ];
});

// Platform-specific options for collaboration types
const placementListArr = computed(() => {
  switch (props.platformType) {
    case 3: // Xiaohongshu
      return [
        { label: "图文笔记", value: 1 },
        { label: "视频笔记", value: 2 }
      ];
    case 4: // Kuaishou
      return [
        { label: "21-60S视频", value: 2 },
        { label: "60S以上视频", value: 3 }
      ];
    case 5: // Bilibili
      return bilibiliCooperationTypes.value;
    case 6: // Tencent
      return [
        { label: "1-60s", value: 1 },
        { label: "60s以上", value: 2 }
      ];
    case 7: // Bilibili
      return [
        { label: "视频投稿", value: 1 },
        { label: "直播带货", value: 2 }
      ];
    default: // Douyin (1 or 2)
      return [
        { label: "1-20S视频", value: 1 },
        { label: "21-60S视频", value: 2 },
        { label: "60S以上视频", value: 71 },
        { label: "招募任务一口价", value: 100 }
      ];
  }
});

const rules = {
  kol_name: [{ required: true, message: '请输入达人名称', trigger: 'blur' }],
  platform_uid: [{ required: true, message: '请输入达人ID', trigger: 'blur' }],
  cooperation_type: [{
    required: () => !isDouyinRecruitmentTask.value, // Not required for recruitment tasks (fixed to 1)
    message: '请选择合作形式',
    trigger: 'change'
  }],
  kol_price: [{ 
    required: true, 
    message: '请输入平台刊例价', 
    trigger: 'blur' 
  }],
  xingtu_task_name: [{ 
    required: () => [1, 2, 6].includes(props.platformType), 
    message: '请输入任务名称', 
    trigger: 'blur' 
  }],
  xingtu_project_name: [{
    required: () => props.platformType === 2,
    message: '请输入项目名称',
    trigger: 'blur'
  }],

  // Recruitment task specific validation rules (for Douyin recruitment mode)
  kol_fans_num: [{
    required: () => isDouyinRecruitmentTask.value,
    message: '请输入达人粉丝量',
    trigger: 'blur'
  }],
  is_xingxiao_kol: [{
    required: () => isDouyinRecruitmentTask.value,
    message: '请输入是否为星晓计划达人',
    trigger: 'blur'
  }],
  kol_licensing_fee: [{
    required: () => isDouyinRecruitmentTask.value,
    message: '请输入达人授权费',
    trigger: 'blur'
  }],
  show_customer_fee: [{
    required: () => isDouyinRecruitmentTask.value,
    message: '请输入对客显示含服务费报价',
    trigger: 'blur'
  }],
  change_status: [{
    required: () => isDouyinRecruitmentTask.value,
    message: '请输入是否改价下单',
    trigger: 'blur'
  }],
  provider_price_exclue_service_fee: [{
    required: () => isDouyinRecruitmentTask.value,
    message: '请输入服务商下单价（不含服务费）',
    trigger: 'blur'
  }],
  provider_price: [{
    required: () => isDouyinRecruitmentTask.value,
    message: '请输入服务商下单价（含服务费）',
    trigger: 'blur'
  }],
  expect_release_time: [{
    required: () => isDouyinRecruitmentTask.value,
    message: '请选择期望发布日期',
    trigger: 'change'
  }],
  predict_receivable_medium_ratio: [{
    required: () => isDouyinRecruitmentTask.value,
    message: '请输入预估应收媒体返点比例',
    trigger: 'blur'
  }],
  customer_rebate_ratio: [{
    required: () => isDouyinRecruitmentTask.value,
    message: '请输入客户返点比例',
    trigger: 'blur'
  }],
  customer_rebate: [{
    required: () => isDouyinRecruitmentTask.value,
    message: '请输入客户返佣',
    trigger: 'blur'
  }],
  customer_service_price: [{
    required: () => isDouyinRecruitmentTask.value,
    message: '请输入客户服务费',
    trigger: 'blur'
  }],
  predict_receivable_customer_price_v1: [{
    required: () => isDouyinRecruitmentTask.value,
    message: '请输入预估应收客户款',
    trigger: 'blur'
  }],
  gross_profit_v1: [{
    required: () => isDouyinRecruitmentTask.value,
    message: '请输入毛利',
    trigger: 'blur'
  }],
  gross_profit_margin_v1: [{
    required: () => isDouyinRecruitmentTask.value,
    message: '请输入毛利率',
    trigger: 'blur'
  }],

  'process_info.kol_base_price': [{
    required: () => !isDouyinRecruitmentTask.value,
    message: '请输入平台刊例价',
    trigger: 'blur'
  }],
  'process_info.kol_licensing_fee': [{ required: true, message: '请输入达人授权费', trigger: 'blur' }],
  'process_info.predict_receivable_medium_ratio': [{ required: true, message: '请输入预估应收媒体返点比例', trigger: 'blur' }],
  'process_info.predict_receivable_medium_price': [{ required: true, message: '请输入预估应收媒体返点金额', trigger: 'blur' }],
  'process_info.bili_service_type': [{
    required: () => isBilibili.value,
    message: '请选择服务类型',
    trigger: 'change'
  }],
  
  'order_info.expect_release_time': [{ 
    required: () => showDouyinOrderInfo.value, 
    message: '请选择期望发布时间', 
    trigger: 'change' 
  }],
  'order_info.expect_save_days': [{ 
    required: () => showDouyinOrderInfo.value, 
    message: '请输入期望保留时长', 
    trigger: 'blur' 
  }],
  
  // 广告ID字段 - 基于选择的广告类型动态设置required
  'order_info.juliang_effect_adv_id': [{
    required: () => showDouyinOrderInfo.value && selectedAdvOptions.value.includes('1'),
    message: '请输入巨量广告主ID-效果广告',
    trigger: 'blur'
  }],
  'order_info.juliang_brand_adv_id': [{
    required: () => showDouyinOrderInfo.value && selectedAdvOptions.value.includes('2'),
    message: '请输入巨量广告主ID-品牌广告',
    trigger: 'blur'
  }],
  'order_info.juliang_qianchuan_adv_id': [{
    required: () => showDouyinOrderInfo.value && selectedAdvOptions.value.includes('3'),
    message: '请输入巨量千川广告主ID',
    trigger: 'blur'
  }],
  'order_info.dou_adv_id': [{
    required: () => showDouyinOrderInfo.value && selectedAdvOptions.value.includes('4'),
    message: '请输入Dou+广告主ID',
    trigger: 'blur'
  }],
  'order_info.dou_adv_uid': [{
    required: () => showDouyinOrderInfo.value && selectedAdvOptions.value.includes('4'),
    message: '请输入Dou+广告主抖音UID',
    trigger: 'blur'
  }],
  
  // Xiaohongshu specific rules
  'order_info.ext.xhs_type': [{ 
    required: () => isXiaohongshu.value, 
    message: '请选择类型', 
    trigger: 'change' 
  }],
  'order_info.ext.xhs_brand_name': [{ 
    required: () => isXiaohongshu.value, 
    message: '请输入报备品牌名', 
    trigger: 'blur' 
  }],
  'order_info.ext.xhs_product_name': [{ 
    required: () => isXiaohongshu.value, 
    message: '请输入产品名称', 
    trigger: 'blur' 
  }],
  'order_info.ext.xhs_publish_date': [{ 
    required: () => isXiaohongshu.value, 
    message: '请选择发布日期', 
    trigger: 'change' 
  }],
  
  // Tencent specific rules
  'order_info.ext.tencent_is_ry_talent': [{ 
    required: () => isTencentMutual.value, 
    message: '请选择是否为如翼R0选号达人', 
    trigger: 'change' 
  }],
  'order_info.ext.tencent_expect_pb_time': [{ 
    required: () => isTencentMutual.value, 
    message: '请选择期望发布时间段', 
    trigger: 'change' 
  }],
  'order_info.ext.tencent_product_name': [{ 
    required: () => isTencentMutual.value, 
    message: '请输入推广产品', 
    trigger: 'blur' 
  }],
  'order_info.ext.tencent_product_desc': [{ 
    required: () => isTencentMutual.value, 
    message: '请输入产品介绍', 
    trigger: 'blur' 
  }],
  'order_info.ext.tencent_promote_target': [{ 
    required: () => isTencentMutual.value, 
    message: '请选择推广目标', 
    trigger: 'change' 
  }],
  
  // Kuaishou specific rules - UPDATED
  'order_info.expect_release_time': [{ 
    required: () => isKuaishou.value, 
    message: '请选择期望发布日期', 
    trigger: 'change' 
  }],
  'order_info.expect_save_days': [{ 
    required: () => isKuaishou.value, 
    message: '请输入期望保留时长', 
    trigger: 'blur' 
  }],
  'order_info.dou_adv_id': [{
    required: () => isKuaishou.value && kuaishouAdvOptions.value.includes('1'),
    message: '请输入磁力智投账号ID',
    trigger: 'blur'
  }],
  'order_info.dou_adv_uid': [{
    required: () => isKuaishou.value && kuaishouAdvOptions.value.includes('2'),
    message: '请输入磁力金牛账号ID',
    trigger: 'blur'
  }],
  
  // Bilibili specific rules
  'order_info.expect_release_time': [{
    required: () => isBilibili.value,
    message: '请选择期望发布时间',
    trigger: 'change'
  }],
  'order_info.ext.bili_contact_information': [{
    required: () => isBilibili.value,
    message: '请输入联系方式',
    trigger: 'blur'
  }],
  'order_info.ext.bili_service_type': [{
    required: () => isBilibili.value,
    message: '请选择服务类型',
    trigger: 'change'
  }],
  'order_info.ext.bili_product_name': [{
    required: () => isBilibili.value,
    message: '请输入产品名称',
    trigger: 'blur'
  }],
  'order_info.ext.bili_product_requirement': [{
    required: false, // Changed from required: () => isBilibili.value to make it non-required
    message: '请输入制作要求',
    trigger: 'blur'
  }]
};

// Computed properties for calculated values
// Star price (total order amount)
const star_price = computed(() => {
  const basePrice = Number(kolForm.value.process_info.kol_base_price) || 0;
  const licensingFee = Number(kolForm.value.process_info.kol_licensing_fee) || 0;
  
  // For Bilibili, use different factors based on service type
  if (isBilibili.value) {
    // 1 = 个人UP主 (7% service fee), 2 = 签约UP主 (5% service fee)
    const serviceType = kolForm.value.process_info.bili_service_type || 1;
    const factor = serviceType === 1 ? 1.07 : 1.05;
    return ((basePrice + licensingFee) * factor).toFixed(2);
  }
  
  // Use 1.1 for Xiaohongshu and 1.05 for others
  const factor = isXiaohongshu.value ? 1.1 : 1.05;
  return ((basePrice + licensingFee) * factor).toFixed(2);
});

// Estimated cost
const predict_cost = computed(() => {
  const actualAmount = Number(kolForm.value.process_info.actual_amount_price) || 0;
  const receiveMedia = Number(kolForm.value.process_info.predict_receivable_medium_price) || 0;
  return (actualAmount - receiveMedia).toFixed(2);
});

// Estimated receivable customer price
const predict_receivable_customer_price = computed(() => {
  const actualAmount = Number(kolForm.value.process_info.actual_amount_price) || 0;
  const customerRebate = Number(kolForm.value.process_info.customer_rebate) || 0;
  const servicePrice = Number(kolForm.value.process_info.customer_service_price) || 0;
  return (actualAmount - customerRebate + servicePrice).toFixed(2);
});

// Gross profit
const gross_profit = computed(() => {
  const receivableCustomer = Number(predict_receivable_customer_price.value) || 0;
  const cost = Number(predict_cost.value) || 0;
  return (receivableCustomer - cost).toFixed(2);
});

// Gross profit margin
const gross_profit_margin = computed(() => {
  const profit = Number(gross_profit.value) || 0;
  const receivableCustomer = Number(predict_receivable_customer_price.value) || 0;

  if (receivableCustomer <= 0) return "0.00";
  return ((profit / receivableCustomer) * 100).toFixed(2);
});



const original_rebate_ratio = computed(() => {
  const basePrice = Number(kolForm.value.process_info.kol_base_price) || 0;
  const actualAmount = Number(kolForm.value.process_info.actual_amount_price) || 0;
  const receiveMedia = Number(kolForm.value.process_info.predict_receivable_medium_price) || 0;
  const mediumRatio = Number(kolForm.value.process_info.predict_receivable_medium_ratio) || 0;
  const changeStatus = Number(kolForm.value.process_info.change_status) || 0;
  
  if (basePrice <= 0) return "0.00";
  
  if (changeStatus === 0) {
    return mediumRatio.toFixed(2);
  }
  
  const factor = isXiaohongshu.value ? 1.1 : 1.05;
  
  // Original rebate formula for price adjustment scenario
  const ratio = ((basePrice - actualAmount / factor + receiveMedia) / basePrice) * 100;
  return ratio > 0 ? ratio.toFixed(2) : "0.00";
});

// Helper function to handle Excel date conversion
const handleExcelDate = (dateValue) => {
  if (!dateValue) return '';

  // If it's already a valid month string (YYYY-MM), return as is
  if (typeof dateValue === 'string' && dateValue.match(/^\d{4}-\d{2}$/)) {
    return dateValue;
  }

  // If it's already a valid date string (YYYY-MM-DD), convert to month format
  if (typeof dateValue === 'string' && dateValue.match(/^\d{4}-\d{2}-\d{2}$/)) {
    return dateValue.substring(0, 7); // Return YYYY-MM format
  }

  // If it's a number (Excel serial date), convert it
  if (typeof dateValue === 'number') {
    // Excel date serial number starts from 1900-01-01
    const excelEpoch = new Date(1900, 0, 1);
    const date = new Date(excelEpoch.getTime() + (dateValue - 1) * 24 * 60 * 60 * 1000);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    return `${year}-${month}`; // Return YYYY-MM format
  }

  // If it's a Date object, convert to month string
  if (dateValue instanceof Date) {
    const year = dateValue.getFullYear();
    const month = String(dateValue.getMonth() + 1).padStart(2, '0');
    return `${year}-${month}`;
  }

  return dateValue;
};

// Event handlers
const handleClose = () => {
  resetForm();
  emit('update:dialogVisible', false);
  emit('cancel');
};



// Update price values based on collaboration type
const updatePriceBasedOnType = () => {
  const price = Number(kolForm.value.kol_price) || 0;
  
  // Update the specific price field based on platform and cooperation type
  if (props.platformType === 1 || props.platformType === 2) {
    // Douyin platform
    if (kolForm.value.cooperation_type === 1) {
      kolForm.value.price_1_20 = price;
    } else if (kolForm.value.cooperation_type === 2) {
      kolForm.value.price_20_60 = price;
    } else if (kolForm.value.cooperation_type === 71 && kolForm.value.price_60) {
      kolForm.value.price_60 = price;
    }
  } else if (props.platformType === 3) {
    // Xiaohongshu
    if (kolForm.value.cooperation_type === 1) {
      kolForm.value.price_1_20 = price; // Text note
    } else if (kolForm.value.cooperation_type === 2) {
      kolForm.value.price_20_60 = price; // Video note
    }
  } else if (props.platformType === 4) {
    // kuaishou
    if (kolForm.value.cooperation_type === 2) {
      kolForm.value.price_20_60 = price; // 21-60s
    } else if (kolForm.value.cooperation_type === 3) {
      kolForm.value.price_60 = price; // 60s+
    }
  }else if (props.platformType === 6) {
    // Tencent
    if (kolForm.value.cooperation_type === 1) {
      kolForm.value.price_1_20 = price; // 1-60s
    } else if (kolForm.value.cooperation_type === 2 && kolForm.value.price_60) {
      kolForm.value.price_60 = price; // 60s+
    }
  }
};

// Update total price based on platform type
const updatePlatformTotalPrice = () => {
  const basePrice = Number(kolForm.value.kol_price) || 0;
  const licensingFee = Number(kolForm.value.process_info.kol_licensing_fee) || 0;
  
  // Calculate platform-specific total prices
  if (isXiaohongshu.value) {
    // Xiaohongshu: (base price + licensing fee) * 1.1
    kolForm.value.order_info.ext.xhs_order_total_price = ((basePrice + licensingFee) * 1.1).toFixed(2);
  } else if (isTencentMutual.value) {
    // Tencent: (base price + licensing fee) * 1.05
    kolForm.value.order_info.ext.tencent_order_total_price = ((basePrice + licensingFee) * 1.05).toFixed(2);
  }
};

// Handle MCN selection
const handleMcnChange = (value) => {
  if (!value) return;
  
  // Handle MCN selection
  if (value.mcn_name && value.mcn_name.includes("\n")) {
    // Multiple entities case
    kolForm.value.mcnObject = {
      mcn_name: value.mcn_name,
      mcn_short_name: value.mcn_short_name,
      mcn_id: value.id,
      mcnNameList: value.mcn_name.split("\n")
    };
    kolForm.value.mcn_name = value.mcn_name.split("\n")[0];
    kolForm.value.mcn_short_name = value.mcn_short_name || "";
    kolForm.value.mcn_id = value.id || "";
    kolForm.value.contact_information = value.mcn_contact_person || kolForm.value.contact_information;
    kolForm.value.kol_attributes = 2; // Set as agency KOL
  } else {
    // Single entity case
    kolForm.value.mcnObject = {
      mcn_name: value.mcn_name || "",
      mcn_short_name: value.mcn_short_name || "",
      mcn_id: value.id || "",
      mcnNameList: [value.mcn_name || ""]
    };
    kolForm.value.mcn_name = value.mcn_name || "";
    kolForm.value.mcn_short_name = value.mcn_short_name || "";
    kolForm.value.mcn_id = value.id || "";
    kolForm.value.contact_information = value.mcn_contact_person || kolForm.value.contact_information;
    kolForm.value.kol_attributes = 2; // Set as agency KOL
  }
};

// Handle advertising options change
const handleAdvOptionsChange = (value) => {
  // Get previous values to compare with new selection
  const previousValues = kolForm.value.order_info.is_publish_adv ? 
    kolForm.value.order_info.is_publish_adv.split(',') : [];
    
  // Check which options were removed
  const removedOptions = previousValues.filter(opt => !value.includes(opt));
  
  // Clear corresponding fields when options are deselected
  if (removedOptions.includes('1')) {
    // If 投放效果广告 (option 1) was unchecked, clear its input field
    kolForm.value.order_info.juliang_effect_adv_id = '';
  }
  
  if (removedOptions.includes('2')) {
    // If 投放品牌广告 (option 2) was unchecked, clear its input field
    kolForm.value.order_info.juliang_brand_adv_id = '';
  }
  
  if (removedOptions.includes('3')) {
    // If 投放巨量千川 (option 3) was unchecked, clear its input field
    kolForm.value.order_info.juliang_qianchuan_adv_id = '';
  }
  
  if (removedOptions.includes('4')) {
    // If 投放至Dou+ (option 4) was unchecked, clear its input fields
    kolForm.value.order_info.dou_adv_id = '';
    kolForm.value.order_info.dou_adv_uid = '';
  }
  
  // Convert the array of selected options to a comma-separated string
  const isPublishAdv = value.join(',');
  kolForm.value.order_info.is_publish_adv = isPublishAdv;
  
  // For Douyin, also store in ext object for compatibility
  if (showDouyinOrderInfo.value) {
    if (!kolForm.value.order_info.ext) {
      kolForm.value.order_info.ext = {};
    }
    kolForm.value.order_info.ext.is_publish_adv = isPublishAdv;
  }
  
  // 触发表单验证
  nextTick(() => {
    if (kolFormRef.value) {
      kolFormRef.value.validateField([
        'order_info.juliang_effect_adv_id',
        'order_info.juliang_brand_adv_id',
        'order_info.juliang_qianchuan_adv_id',
        'order_info.dou_adv_id',
        'order_info.dou_adv_uid'
      ]);
    }
  });
};

// Update price adjustment status
const radioChange = () => {
  if (kolForm.value.process_info.change_status === 1) {
    if (!kolForm.value.process_info.actual_amount_price) {
      kolForm.value.process_info.actual_amount_price = Number(Number(star_price.value).toFixed(2));
    }
  } else {
    kolForm.value.process_info.actual_amount_price = Number(Number(star_price.value).toFixed(2));
  }
  
  // Update dependent calculations
  syncCalculatedValues();
};

// Update media ratio and recalculate price
const changeMediumRatio = (val) => {
  const ratio = Number(val).toFixed(2);
  kolForm.value.process_info.predict_receivable_medium_ratio = ratio;
  
  // Calculate media rebate price based on ratio
  const basePrice = Number(kolForm.value.process_info.kol_base_price) || 0;
  kolForm.value.process_info.predict_receivable_medium_price = ((ratio / 100) * basePrice).toFixed(2);
  
  // Update dependent calculations
  syncCalculatedValues();
};

// Update media price and recalculate ratio
const changeMediumPrice = (val) => {
  // Handle both direct value and event target value
  const inputValue = typeof val === 'object' && val !== null ? val.target?.value : val;
  const price = Number(inputValue || 0).toFixed(2);
  kolForm.value.process_info.predict_receivable_medium_price = price;

  // Calculate ratio based on price
  const basePrice = Number(kolForm.value.process_info.kol_base_price) || 0;
  if (basePrice > 0) {
    kolForm.value.process_info.predict_receivable_medium_ratio = ((price / basePrice) * 100).toFixed(2);
  }

  // Update dependent calculations
  syncCalculatedValues();
};

// Handle medium price input - prevent formatting during input
const handleMediumPriceInput = (val) => {
  // During input, just store the raw value without formatting
  // This prevents the cursor from jumping and allows continuous input
  const inputValue = typeof val === 'string' ? val : String(val || '');
  kolForm.value.process_info.predict_receivable_medium_price = inputValue;
};

// Handle medium price blur - format and calculate when user finishes input
const handleMediumPriceBlur = () => {
  // Format the value and trigger calculations when user leaves the field
  const currentValue = kolForm.value.process_info.predict_receivable_medium_price;
  if (currentValue === '' || currentValue === null || currentValue === undefined) {
    return;
  }

  // Format to 2 decimal places
  const formattedValue = Number(currentValue || 0).toFixed(2);
  kolForm.value.process_info.predict_receivable_medium_price = formattedValue;

  // Calculate ratio based on price
  const basePrice = Number(kolForm.value.process_info.kol_base_price) || 0;
  if (basePrice > 0) {
    kolForm.value.process_info.predict_receivable_medium_ratio = ((formattedValue / basePrice) * 100).toFixed(2);
  }

  // Update dependent calculations
  syncCalculatedValues();
};

// Synchronize all calculated values to the form
const syncCalculatedValues = () => {
  // Update star price (calculated)
  kolForm.value.process_info.star_price = Number(star_price.value);
  
  // Update estimated cost (calculated)
  kolForm.value.process_info.predict_cost = Number(predict_cost.value);
  
  // Update estimated receivable customer price (calculated)
  kolForm.value.process_info.predict_receivable_customer_price = Number(predict_receivable_customer_price.value);
  
  // Update gross profit (calculated)
  kolForm.value.process_info.gross_profit = Number(gross_profit.value);
  
  // Update gross profit margin (calculated)
  kolForm.value.process_info.gross_profit_margin = Number(gross_profit_margin.value);
  
  // Update original rebate ratio (calculated)
  kolForm.value.process_info.original_rebate_ratio = Number(original_rebate_ratio.value);
  // 修正 actual_amount_price 精度
  kolForm.value.process_info.actual_amount_price = kolForm.value.process_info.actual_amount_price === '' ? '' : Number(Number(kolForm.value.process_info.actual_amount_price).toFixed(2));
};

// Calculate price after kol_licensing_fee changes
const amountPriceCalculate = () => {
  // Only update if not manually adjusting price
  if (kolForm.value.process_info.change_status === 0) {
    // Update actual amount price based on star price
    nextTick(() => {
      kolForm.value.process_info.actual_amount_price = Number(Number(star_price.value).toFixed(2));
    });
  }
  
  // Update platform-specific total prices
  updatePlatformTotalPrice();
  
  // Update dependent calculations
  syncCalculatedValues();
};

// Handle file upload for attachments
const uploadRequest = (files) => {
  let formData = new FormData();
  formData.append("file", files.file);
  
  uploadMcnAnnex(formData).then(res => {
    if (kolForm.value.process_info.img_url && kolForm.value.process_info.img_url.length > 0) {
      kolForm.value.process_info.img_url += "," + res.data;
    } else {
      kolForm.value.process_info.img_url = res.data;
    }
  });
};

// Handle image preview
const handlePictureCardPreview = (uploadFile) => {
  dialogImageUrl.value = uploadFile.url;
  dialogImageVisible.value = true;
};

// Handle image removal
const handleRemove = () => {
  kolForm.value.process_info.img_url = "";
};

// Reset form to initial state
const resetForm = () => {
  if (kolFormRef.value) {
    kolFormRef.value.resetFields();
  }
  
  // Reset selected advertising options
  selectedAdvOptions.value = [];
  kuaishouAdvOptions.value = [];
  
  // Reset form data
  kolForm.value = {
    kol_name: '',
    platform_uid: '',
    cooperation_type: isDouyinRecruitmentTask.value ? 1 : null, // Fixed to 1 for recruitment tasks, otherwise null
    xingtu_task_name: '',
    xingtu_project_name: '', // Reset the new field
    kol_price: 0,
    alliance_personnel: '',
    alliance_personnel_id: '',
    mcnObject: {
      mcn_name: '',
      mcn_short_name: '',
      mcn_id: ''
    },
    mcn_name: '',
    mcn_short_name: '',
    mcn_id: '',
    contact_information: '',
    kol_num: 1,
    price_1_20: 0,
    price_20_60: 0,
    price_60: 0,
    rebate_ratio: 0,
    kol_attributes: 1,
    kol_type: 1,
    collection_status: 1,

    // Reset recruitment task specific fields
    kol_fans_num: '',
    is_xingxiao_kol: '',
    kol_licensing_fee: 0,
    show_customer_fee: 0,
    change_status: '',
    provider_price_exclue_service_fee: 0,
    provider_price: 0,
    expect_release_time: '',
    predict_receivable_medium_ratio: 0,
    customer_rebate_ratio: 0,
    customer_rebate: 0,
    customer_service_price: 0,
    predict_receivable_customer_price: 0,
    gross_profit: 0,
    gross_profit_margin: 0,

    process_info: {
      kol_base_price: 0,
      kol_licensing_fee: 0,
      change_status: 0,
      actual_amount_price: 0,
      predict_receivable_medium_price: 0,
      predict_receivable_medium_ratio: 0,
      customer_rebate: 0,
      customer_service_price: 0,
      special_rebate_amount: '',
      other_notes: '',
      gross_profit: 0,
      gross_profit_margin: 0,
      original_rebate_ratio: 0,
      star_price: 0,
      predict_cost: 0,
      predict_receivable_customer_price: 0,
      img_url: '',
      bili_service_type: 1
    },
    
    order_info: {
      expect_release_time: '',
      expect_save_days: '',
      juliang_effect_adv_id: '',
      juliang_brand_adv_id: '',
      juliang_qianchuan_adv_id: '',
      dou_adv_id: '',
      dou_adv_uid: '',
      component_type: '',
      component_content: '',
      component_url: '',
      component_remark: '',
      extra_remark: '',
      is_publish_adv: '', // Database doesn't allow null
      ext: {
        // Xiaohongshu fields
        xhs_type: '',
        xhs_brand_name: '',
        xhs_product_name: '',
        xhs_order_total_price: 0,
        xhs_publish_date: '',
        xhs_ex_remark: '',
        xhs_bind_spu: '',
        xhs_product_id: '',
        
        // Tencent fields
        tencent_number: '',
        tencent_is_ry_talent: '',
        tencent_expect_pb_time: '',
        tencent_order_total_price: 0,
        tencent_product_name: '',
        tencent_product_desc: '',
        tencent_video_rquirement: '',
        tencent_outside_video_desc: '',
        tencent_script_confirm: '',
        tencent_promotion_scene: '',
        tencent_enable_adv_component: '',
        tencent_component_info: '',
        tencent_enable_second_promotion: '',
        tencent_market_target: null, // 营销目标
        tencent_promote_target: null, // 推广目标
        
        // Douyin fields - for platforms other than Kuaishou
        expect_release_time: '',
        expect_save_days: '',
        is_publish_adv: '',
        juliang_effect_adv_id: '',
        juliang_brand_adv_id: '',
        juliang_qianchuan_adv_id: '',
        dou_adv_id: '',
        dou_adv_uid: '',
        component_type: null,
        component_content: '',
        component_url: '',
        component_remark: '',
        extra_remark: '',
        
        bili_service_type: 1,
        bili_product_name: '',
        bili_contact_information: '',
        bili_product_requirement: '',
        bili_notes: ''
      }
    }
  };
  
  fileList.value = [];
  
  activeTab.value = 'basic';
};

// Load recruitment task profit info separately to avoid interference
const loadRecruitmentProfitInfo = () => {
  console.log('=== loadRecruitmentProfitInfo called ===');
  console.log('isDouyinRecruitmentTask:', isDouyinRecruitmentTask.value);
  console.log('props.rowData:', props.rowData);
  console.log('props.rowData.process_info:', props.rowData?.process_info);

  if (!isDouyinRecruitmentTask.value) {
    console.log('❌ Not a Douyin recruitment task, skipping');
    return;
  }

  if (!props.rowData?.process_info) {
    console.log('❌ No process_info found, skipping');
    return;
  }

  console.log('=== Loading recruitment profit info ===');
  console.log('process_info:', props.rowData.process_info);
  console.log('v1_fields from backend:', {
    predict_receivable_customer_price_v1: props.rowData.process_info.predict_receivable_customer_price_v1,
    gross_profit_v1: props.rowData.process_info.gross_profit_v1,
    gross_profit_margin_v1: props.rowData.process_info.gross_profit_margin_v1
  });

  // Directly set _v1 fields from process_info for recruitment tasks
  if (props.rowData.process_info.predict_receivable_customer_price_v1 !== undefined) {
    kolForm.value.predict_receivable_customer_price_v1 = props.rowData.process_info.predict_receivable_customer_price_v1;
    console.log('✅ Set predict_receivable_customer_price_v1:', kolForm.value.predict_receivable_customer_price_v1);
  } else {
    console.log('❌ predict_receivable_customer_price_v1 is undefined');
  }

  if (props.rowData.process_info.gross_profit_v1 !== undefined) {
    kolForm.value.gross_profit_v1 = props.rowData.process_info.gross_profit_v1;
    console.log('✅ Set gross_profit_v1:', kolForm.value.gross_profit_v1);
  } else {
    console.log('❌ gross_profit_v1 is undefined');
  }

  if (props.rowData.process_info.gross_profit_margin_v1 !== undefined) {
    kolForm.value.gross_profit_margin_v1 = props.rowData.process_info.gross_profit_margin_v1;
    console.log('✅ Set gross_profit_margin_v1:', kolForm.value.gross_profit_margin_v1);
  } else {
    console.log('❌ gross_profit_margin_v1 is undefined');
  }

  // Also load other recruitment-specific profit fields
  const recruitmentProfitFields = [
    'kol_licensing_fee',
    'customer_rebate',
    'customer_service_price',
    'predict_receivable_medium_ratio',
    'change_status'
  ];

  recruitmentProfitFields.forEach(field => {
    if (props.rowData.process_info[field] !== undefined) {
      kolForm.value[field] = props.rowData.process_info[field];
      console.log(`✅ Set recruitment field ${field}:`, kolForm.value[field]);
    } else {
      console.log(`❌ Field ${field} is undefined in process_info`);
    }
  });

  console.log('=== Recruitment profit info loading completed ===');
  console.log('Final form values:', {
    predict_receivable_customer_price_v1: kolForm.value.predict_receivable_customer_price_v1,
    gross_profit_v1: kolForm.value.gross_profit_v1,
    gross_profit_margin_v1: kolForm.value.gross_profit_margin_v1
  });

  // Force reactivity update
  nextTick(() => {
    console.log('After nextTick - Final form values:', {
      predict_receivable_customer_price_v1: kolForm.value.predict_receivable_customer_price_v1,
      gross_profit_v1: kolForm.value.gross_profit_v1,
      gross_profit_margin_v1: kolForm.value.gross_profit_margin_v1
    });
  });
};

const loadRowData = () => {
  console.log('loadRowData called with:', props.rowData);
  console.log('isDouyinRecruitmentTask:', isDouyinRecruitmentTask.value);
  console.log('platformType:', props.platformType, 'taskType:', props.taskType);

  if (!props.rowData || Object.keys(props.rowData).length === 0) return;

  // Initialize order_info structure
  if (!kolForm.value.order_info) {
    kolForm.value.order_info = {};
  }

  if (!kolForm.value.order_info.ext) {
    kolForm.value.order_info.ext = {};
  }

  // Handle basic fields first - this is crucial for basic info rendering
  const basicFields = [
    'kol_name', 'platform_uid', 'cooperation_type', 'xingtu_task_name', 'xingtu_project_name',
    'kol_price', 'alliance_personnel', 'alliance_personnel_id',
    'contact_information', 'kol_num', 'price_1_20', 'price_20_60', 'price_60',
    'rebate_ratio', 'kol_attributes', 'kol_type', 'collection_status',
    'mcn_name', 'mcn_short_name', 'mcn_id'
  ];

  basicFields.forEach(field => {
    if (props.rowData[field] !== undefined) {
      kolForm.value[field] = props.rowData[field];
    }
  });

  // 调试日志：检查基础字段设置
  console.log('基础字段设置完成:', {
    kol_name: kolForm.value.kol_name,
    platform_uid: kolForm.value.platform_uid,
    cooperation_type: kolForm.value.cooperation_type,
    kol_price: kolForm.value.kol_price,
    alliance_personnel: kolForm.value.alliance_personnel,
    contact_information: kolForm.value.contact_information
  });

  // Handle MCN object
  if (props.rowData.mcnObject) {
    kolForm.value.mcnObject = JSON.parse(JSON.stringify(props.rowData.mcnObject));

    // 设置达人属性（机构达人）
    if (props.rowData.mcnObject.mcn_short_name || props.rowData.mcn_short_name) {
      kolForm.value.kol_attributes = 2; // 机构达人
    }
  }

  // Handle recruitment task specific fields for Douyin recruitment mode
  if (isDouyinRecruitmentTask.value) {
    // Fields that are typically in ext object (from API response structure)
    const extFields = [
      'kol_fans_num',
      'is_xingxiao_kol',
      'show_customer_fee',
      'provider_price_exclue_service_fee',
      'provider_price',
      'customer_rebate_ratio'
    ];

    // Fields that are typically in process_info object
    const processInfoFields = [
      'kol_licensing_fee',
      'change_status',
      'predict_receivable_medium_ratio',
      'customer_rebate',
      'customer_service_price'
    ];

    // Fields that are typically at root level
    const rootFields = [
      'expect_release_time'
    ];

    // Handle ext fields (priority: ext -> root -> process_info)
    extFields.forEach(field => {
      let fieldValue = undefined;

      if (props.rowData.ext && props.rowData.ext[field] !== undefined) {
        fieldValue = props.rowData.ext[field];
      }
      else if (props.rowData[field] !== undefined) {
        fieldValue = props.rowData[field];
      }
      else if (props.rowData.process_info && props.rowData.process_info[field] !== undefined) {
        fieldValue = props.rowData.process_info[field];
      }

      if (fieldValue !== undefined) {
        kolForm.value[field] = fieldValue;
      }
    });

    // Handle process_info fields (priority: process_info -> root -> ext)
    processInfoFields.forEach(field => {
      let fieldValue = undefined;

      if (props.rowData.process_info && props.rowData.process_info[field] !== undefined) {
        fieldValue = props.rowData.process_info[field];
      }
      else if (props.rowData[field] !== undefined) {
        fieldValue = props.rowData[field];
      }
      else if (props.rowData.ext && props.rowData.ext[field] !== undefined) {
        fieldValue = props.rowData.ext[field];
      }

      if (fieldValue !== undefined) {
        kolForm.value[field] = fieldValue;
      }
    });

    // Handle root fields (priority: root -> ext -> process_info)
    rootFields.forEach(field => {
      let fieldValue = undefined;

      if (props.rowData[field] !== undefined) {
        fieldValue = props.rowData[field];
      }
      else if (props.rowData.ext && props.rowData.ext[field] !== undefined) {
        fieldValue = props.rowData.ext[field];
      }
      else if (props.rowData.process_info && props.rowData.process_info[field] !== undefined) {
        fieldValue = props.rowData.process_info[field];
      }

      if (fieldValue !== undefined) {
        kolForm.value[field] = fieldValue;
      }
    });



    // Handle date field specifically for recruitment
    if (props.rowData.expect_release_time) {
      kolForm.value.expect_release_time = handleExcelDate(props.rowData.expect_release_time);
    }

    // Handle special field conversions for recruitment tasks
    // These fields need to be mapped to both root level (for display) and process_info (for calculation)
    const dualMappingFields = [
      'change_status',
      'predict_receivable_medium_ratio',
      'customer_rebate',
      'customer_service_price'
    ];

    dualMappingFields.forEach(field => {
      let fieldValue = undefined;

      // Check multiple sources for the field value - priority order:
      // 1. Root level (highest priority)
      if (props.rowData[field] !== undefined) {
        fieldValue = props.rowData[field];
      }
      // 2. ext object (for collected talent data)
      else if (props.rowData.ext && props.rowData.ext[field] !== undefined) {
        fieldValue = props.rowData.ext[field];
      }
      // 3. process_info object
      else if (props.rowData.process_info && props.rowData.process_info[field] !== undefined) {
        fieldValue = props.rowData.process_info[field];
      }

      // Set the field value if found
      if (fieldValue !== undefined) {
        if (field === 'change_status') {
          // Convert "是"/"否" to 1/0 for change_status
          const convertedValue = fieldValue === '是' ? 1 : (fieldValue === '否' ? 0 : fieldValue);
          kolForm.value[field] = fieldValue; // Keep original value for display
          kolForm.value.process_info[field] = convertedValue; // Convert for calculation
        } else {
          // For other fields, set both root level and process_info
          kolForm.value[field] = fieldValue;
          kolForm.value.process_info[field] = fieldValue;
        }
      }
    });

    // Handle other recruitment-specific fields
    if (props.rowData.is_xingxiao_kol !== undefined) {
      kolForm.value.is_xingxiao_kol = props.rowData.is_xingxiao_kol;
    }

    // Fixed cooperation_type to 1 for recruitment tasks (hidden from UI)
    kolForm.value.cooperation_type = 1;

    // Set default active tab to recruitment for recruitment tasks
    activeTab.value = 'recruitment';
  }

  // Handle is_publish_adv from various sources
  let isPublishAdvValue = undefined;

  if (props.rowData.is_publish_adv !== undefined && props.rowData.is_publish_adv !== null) {
    isPublishAdvValue = props.rowData.is_publish_adv || "";
  }
  else if (props.rowData.order_info?.is_publish_adv !== undefined && props.rowData.order_info?.is_publish_adv !== null) {
    isPublishAdvValue = props.rowData.order_info.is_publish_adv || "";
  }
  else if (props.rowData.order_info?.ext?.is_publish_adv !== undefined && props.rowData.order_info?.ext?.is_publish_adv !== null) {
    isPublishAdvValue = props.rowData.order_info.ext.is_publish_adv || "";
  }

  if (isPublishAdvValue !== undefined && isPublishAdvValue !== null && isPublishAdvValue !== '') {
    kolForm.value.order_info.is_publish_adv = isPublishAdvValue || "";

    if (isKuaishou.value) {
      kuaishouAdvOptions.value = isPublishAdvValue.toString().split(',').map(String);
    } else {
      selectedAdvOptions.value = isPublishAdvValue.toString().split(',').map(String);
    }
  } else {
    // Initialize with empty string for Douyin offline orders (database doesn't allow null)
    if (showDouyinOrderInfo.value) {
      kolForm.value.order_info.is_publish_adv = '';
      selectedAdvOptions.value = [];
    }
  }
  
  // Handle process_info (profit information) loading
  if (props.rowData.process_info) {
    // For recruitment tasks, copy all fields including _v1 fields
    if (isDouyinRecruitmentTask.value) {
      Object.keys(props.rowData.process_info).forEach(key => {
        kolForm.value.process_info[key] = props.rowData.process_info[key];
      });
    } else {
      // For non-recruitment tasks, only copy existing fields
      Object.keys(props.rowData.process_info).forEach(key => {
        if (kolForm.value.process_info.hasOwnProperty(key)) {
          kolForm.value.process_info[key] = props.rowData.process_info[key];
        }
      });
    }

    // Ensure kol_base_price is synchronized with kol_price
    kolForm.value.process_info.kol_base_price = kolForm.value.kol_price || props.rowData.process_info.kol_base_price || 0;

    if (kolForm.value.process_info.change_status === 0 || !kolForm.value.process_info.actual_amount_price) {
      nextTick(() => {
        kolForm.value.process_info.actual_amount_price = Number(Number(star_price.value).toFixed(2));
        syncCalculatedValues();
      });
    }

    if (props.rowData.process_info.img_url) {
      const images = props.rowData.process_info.img_url.split(',');
      fileList.value = images.map(url => ({
        name: url.split('/').pop(),
        url: url
      }));
    }
  } else {
    // Fallback: try to load process fields from root level
    const processFields = [
      'kol_base_price',
      'kol_licensing_fee',
      'change_status',
      'actual_amount_price',
      'predict_receivable_medium_price',
      'predict_receivable_medium_ratio',
      'customer_rebate',
      'customer_service_price',
      'bili_service_type',
      // Add additional fields that might be at root level from imported data
      'provider_price_exclue_service_fee',
      'provider_price',
      'customer_rebate_ratio',
      'show_customer_fee',
      'predict_receivable_customer_price',
      'gross_profit',
      'gross_profit_margin',
      'original_rebate_ratio',
      'special_rebate_amount',
      'other_notes'
    ];

    processFields.forEach(field => {
      if (props.rowData[field] !== undefined) {
        if (field === 'change_status') {
          kolForm.value.process_info[field] = props.rowData[field] === '是' ? 1 : 0;
        } else {
          kolForm.value.process_info[field] = props.rowData[field];
        }
      }
    });

    // Ensure kol_base_price is synchronized with kol_price
    kolForm.value.process_info.kol_base_price = kolForm.value.kol_price || 0;
  }

  // Additional handling for imported data fields that might be at root level
  // This ensures compatibility with imported talent data structure
  const additionalProcessFields = [
    'kol_licensing_fee',
    'change_status',
    'customer_rebate',
    'customer_service_price',
    'predict_receivable_medium_ratio',
    'customer_rebate_ratio',
    'show_customer_fee',
    'provider_price_exclue_service_fee',
    'provider_price',
    'predict_receivable_customer_price',
    'gross_profit',
    'gross_profit_margin'
  ];

  additionalProcessFields.forEach(field => {
    let fieldValue = undefined;

    // Check multiple sources for the field value - priority order:
    // 1. Root level (highest priority)
    if (props.rowData[field] !== undefined) {
      fieldValue = props.rowData[field];
    }
    // 2. ext object (for collected talent data)
    else if (props.rowData.ext && props.rowData.ext[field] !== undefined) {
      fieldValue = props.rowData.ext[field];
    }
    // 3. process_info object
    else if (props.rowData.process_info && props.rowData.process_info[field] !== undefined) {
      fieldValue = props.rowData.process_info[field];
    }

    // Set the field value if found
    if (fieldValue !== undefined) {
      if (field === 'change_status') {
        kolForm.value.process_info[field] = fieldValue === '是' ? 1 : (fieldValue === '否' ? 0 : fieldValue);
      } else {
        kolForm.value.process_info[field] = fieldValue;
      }
    }
  });

  // Special handling for imported talent data - ensure recruitment fields are properly mapped
  if (isDouyinRecruitmentTask.value) {
    // Map additional recruitment fields that might be at root level or in ext object
    const importedRecruitmentFields = [
      'kol_fans_num',
      'is_xingxiao_kol',
      'show_customer_fee',
      'provider_price_exclue_service_fee',
      'provider_price',
      'customer_rebate_ratio'
    ];

    importedRecruitmentFields.forEach(field => {
      let fieldValue = undefined;

      // Check multiple sources for the field value - priority order:
      // 1. Root level (highest priority)
      if (props.rowData[field] !== undefined) {
        fieldValue = props.rowData[field];
      }
      // 2. ext object (for collected talent data)
      else if (props.rowData.ext && props.rowData.ext[field] !== undefined) {
        fieldValue = props.rowData.ext[field];
      }
      // 3. process_info object
      else if (props.rowData.process_info && props.rowData.process_info[field] !== undefined) {
        fieldValue = props.rowData.process_info[field];
      }

      // Set the field value if found
      if (fieldValue !== undefined) {
        kolForm.value[field] = fieldValue;
      }
    });

    // Ensure numeric fields are properly converted
    if (kolForm.value.kol_fans_num !== undefined) {
      kolForm.value.kol_fans_num = Number(kolForm.value.kol_fans_num) || 0;
    }
    if (kolForm.value.show_customer_fee !== undefined) {
      kolForm.value.show_customer_fee = Number(kolForm.value.show_customer_fee) || 0;
    }
    if (kolForm.value.provider_price_exclue_service_fee !== undefined) {
      kolForm.value.provider_price_exclue_service_fee = Number(kolForm.value.provider_price_exclue_service_fee) || 0;
    }
    if (kolForm.value.provider_price !== undefined) {
      kolForm.value.provider_price = Number(kolForm.value.provider_price) || 0;
    }
    if (kolForm.value.customer_rebate_ratio !== undefined) {
      kolForm.value.customer_rebate_ratio = Number(kolForm.value.customer_rebate_ratio) || 0;
    }
  }
  
  if (!kolForm.value.order_info) {
    kolForm.value.order_info = { ext: {} };
  }
  
  if (!kolForm.value.order_info.ext) {
    kolForm.value.order_info.ext = {};
  }
  
  // Handle order_info data loading
  if (props.rowData.order_info) {
    if (isKuaishou.value) {
      const kuaishouFields = [
        'expect_release_time',
        'expect_save_days',
        'is_publish_adv',
        'dou_adv_id',
        'dou_adv_uid',
        'component_type',
        'component_content',
        'component_url',
        'component_remark',
        'extra_remark'
      ];

      kuaishouFields.forEach(field => {
        if (props.rowData.order_info[field] !== undefined) {
          kolForm.value.order_info[field] = props.rowData.order_info[field];
        }
      });
    } else if (showDouyinOrderInfo.value) {
      // For Douyin offline orders, keep data directly in order_info
      const douyinFields = [
        'expect_release_time',
        'expect_save_days',
        'is_publish_adv',
        'juliang_effect_adv_id',
        'juliang_brand_adv_id',
        'juliang_qianchuan_adv_id',
        'dou_adv_id',
        'dou_adv_uid',
        'component_type',
        'component_content',
        'component_url',
        'component_remark',
        'extra_remark',
        'xingtu_project_name'
      ];

      douyinFields.forEach(field => {
        if (props.rowData.order_info[field] !== undefined) {
          kolForm.value.order_info[field] = props.rowData.order_info[field];
        }
      });
    } else if (props.rowData.order_info.ext) {
      Object.keys(props.rowData.order_info.ext).forEach(key => {
        kolForm.value.order_info.ext[key] = props.rowData.order_info.ext[key];
      });
    }
  }

  // Enhanced handling for Douyin offline orders
  if (showDouyinOrderInfo.value) {
    // Define all possible fields for Douyin offline orders
    const douyinFields = [
      'expect_release_time',
      'expect_save_days',
      'is_publish_adv',
      'juliang_effect_adv_id',
      'juliang_brand_adv_id',
      'juliang_qianchuan_adv_id',
      'dou_adv_id',
      'dou_adv_uid',
      'component_type',
      'component_content',
      'component_url',
      'component_remark',
      'extra_remark',
      'xingtu_project_name'
    ];

    // Collect data from all possible sources in priority order
    douyinFields.forEach(field => {
      let fieldValue = undefined;

      // Check all possible locations - priority order:
      // 1. props.rowData directly (highest priority)
      if (props.rowData[field] !== undefined) {
        fieldValue = props.rowData[field];
      }
      // 2. order_info
      else if (props.rowData.order_info && props.rowData.order_info[field] !== undefined) {
        fieldValue = props.rowData.order_info[field];
      }
      // 3. order_info.ext
      else if (props.rowData.order_info?.ext && props.rowData.order_info.ext[field] !== undefined) {
        fieldValue = props.rowData.order_info.ext[field];
      }
      // 4. props.rowData.ext
      else if (props.rowData.ext && props.rowData.ext[field] !== undefined) {
        fieldValue = props.rowData.ext[field];
      }

      // Set the field value if found
      if (fieldValue !== undefined) {
        kolForm.value.order_info[field] = fieldValue;
      }
    });

    // Ensure all required Douyin order fields have default values if still not present
    const douyinDefaults = {
      expect_release_time: '',
      expect_save_days: 1,
      component_type: 4,
      component_content: '',
      component_url: '',
      component_remark: '',
      extra_remark: '',
      xingtu_project_name: '',
      is_publish_adv: '' // Database doesn't allow null, use empty string
    };

    // Apply defaults for missing fields
    Object.keys(douyinDefaults).forEach(field => {
      if (kolForm.value.order_info[field] === undefined || kolForm.value.order_info[field] === null || kolForm.value.order_info[field] === '') {
        kolForm.value.order_info[field] = douyinDefaults[field];
      }
    });

    // Ensure numeric fields are properly typed
    if (kolForm.value.order_info.expect_save_days !== undefined) {
      kolForm.value.order_info.expect_save_days = Number(kolForm.value.order_info.expect_save_days);
    }
    if (kolForm.value.order_info.component_type !== undefined) {
      kolForm.value.order_info.component_type = Number(kolForm.value.order_info.component_type);
    }
  }
  
  // Handle advertising options for different platforms
  if (isKuaishou.value) {
    const advValue = kolForm.value.order_info.is_publish_adv ||
                     (kolForm.value.order_info.ext && kolForm.value.order_info.ext.is_publish_adv) ||
                     props.rowData.is_publish_adv ||
                     (props.rowData.ext && props.rowData.ext.is_publish_adv) || '';

    if (advValue) {
      kuaishouAdvOptions.value = advValue.split(',').map(String);
    }
  } else if (showDouyinOrderInfo.value) {
    // For Douyin offline orders, check multiple sources for is_publish_adv
    const advValue = kolForm.value.order_info.is_publish_adv ||
                     (kolForm.value.order_info.ext && kolForm.value.order_info.ext.is_publish_adv) ||
                     props.rowData.is_publish_adv ||
                     (props.rowData.order_info && props.rowData.order_info.is_publish_adv) ||
                     (props.rowData.ext && props.rowData.ext.is_publish_adv) || '';

    if (advValue) {
      selectedAdvOptions.value = advValue.split(',').map(String);
      // Also ensure it's stored in the form
      kolForm.value.order_info.is_publish_adv = advValue || "";
    }
  }
  
  if (isBilibili.value) {
    if (props.rowData.expect_release_time) {
      kolForm.value.order_info.expect_release_time = props.rowData.expect_release_time;
    } else if (props.rowData.order_info?.expect_release_time) {
      kolForm.value.order_info.expect_release_time = props.rowData.order_info.expect_release_time;
    } else if (props.rowData.order_info?.ext?.expect_release_time) {
      kolForm.value.order_info.expect_release_time = props.rowData.order_info.ext.expect_release_time;
    }

    // Enhanced B站服务类型处理 - 检查所有可能的数据源
    if (props.rowData.bili_service_type !== undefined) {
      kolForm.value.process_info.bili_service_type = Number(props.rowData.bili_service_type);
    } else if (props.rowData.process_info?.bili_service_type !== undefined) {
      kolForm.value.process_info.bili_service_type = Number(props.rowData.process_info.bili_service_type);
    } else if (props.rowData.order_info?.ext?.bili_service_type !== undefined) {
      kolForm.value.process_info.bili_service_type = Number(props.rowData.order_info.ext.bili_service_type);
    } else if (props.rowData.ext?.bili_service_type !== undefined) {
      kolForm.value.process_info.bili_service_type = Number(props.rowData.ext.bili_service_type);
    } else {
      // 如果没有找到服务类型数据，设置默认值
      kolForm.value.process_info.bili_service_type = 1;
    }

    // 处理B站下单信息中的其他字段
    const biliOrderFields = [
      'bili_contact_information',
      'bili_product_name',
      'bili_product_requirement',
      'bili_notes'
    ];

    biliOrderFields.forEach(field => {
      if (props.rowData[field] !== undefined) {
        kolForm.value.order_info.ext[field] = props.rowData[field];
      } else if (props.rowData.order_info?.ext?.[field] !== undefined) {
        kolForm.value.order_info.ext[field] = props.rowData.order_info.ext[field];
      } else if (props.rowData.ext?.[field] !== undefined) {
        kolForm.value.order_info.ext[field] = props.rowData.ext[field];
      }
    });
  }
  
  // Ensure all required fields have values for proper rendering
  nextTick(() => {
    // Ensure basic info fields are properly set
    if (!kolForm.value.kol_name && props.rowData.kol_name) {
      kolForm.value.kol_name = props.rowData.kol_name;
    }
    if (!kolForm.value.platform_uid && props.rowData.platform_uid) {
      kolForm.value.platform_uid = props.rowData.platform_uid;
    }
    if (!kolForm.value.cooperation_type && props.rowData.cooperation_type) {
      kolForm.value.cooperation_type = props.rowData.cooperation_type;
    }

    // Ensure process_info is properly initialized
    if (!kolForm.value.process_info.kol_base_price && kolForm.value.kol_price) {
      kolForm.value.process_info.kol_base_price = kolForm.value.kol_price;
    }

    // For Douyin offline orders, ensure all required order fields exist
    if (showDouyinOrderInfo.value) {
      const requiredOrderFields = {
        expect_release_time: props.rowData.order_info?.expect_release_time || '',
        expect_save_days: Number(props.rowData.order_info?.expect_save_days) || 1,
        component_type: Number(props.rowData.order_info?.component_type) || 4,
        is_publish_adv: props.rowData.order_info?.is_publish_adv || '' // Database doesn't allow null
      };

      Object.keys(requiredOrderFields).forEach(field => {
        if (kolForm.value.order_info[field] === undefined || kolForm.value.order_info[field] === null || kolForm.value.order_info[field] === '') {
          kolForm.value.order_info[field] = requiredOrderFields[field];
        }
      });

      // Force update reactive values
      nextTick(() => {
        // Ensure the form fields are properly bound
        if (kolForm.value.order_info.expect_release_time && !kolForm.value.order_info.expect_release_time.includes('-')) {
          // Handle date format if needed
        }

        // Ensure advertising options are properly set
        if (kolForm.value.order_info.is_publish_adv && kolForm.value.order_info.is_publish_adv !== null && kolForm.value.order_info.is_publish_adv !== '') {
          selectedAdvOptions.value = kolForm.value.order_info.is_publish_adv.toString().split(',').map(String);
        } else {
          selectedAdvOptions.value = [];
        }
      });
    }

    // Final synchronization
    syncCalculatedValues();

    // Final validation for recruitment tasks - ensure all required fields have values
    if (isDouyinRecruitmentTask.value) {
      // Set default values for any missing recruitment fields
      const recruitmentDefaults = {
        kol_fans_num: 0,
        is_xingxiao_kol: '',
        kol_licensing_fee: 0,
        show_customer_fee: 0,
        change_status: '',
        provider_price_exclue_service_fee: 0,
        provider_price: 0,
        expect_release_time: '',
        predict_receivable_medium_ratio: 0,
        customer_rebate_ratio: 0,
        customer_rebate: 0,
        customer_service_price: 0
      };

      Object.keys(recruitmentDefaults).forEach(field => {
        if (kolForm.value[field] === undefined || kolForm.value[field] === null) {
          kolForm.value[field] = recruitmentDefaults[field];
        }
      });

      // Load recruitment profit info using dedicated function
      loadRecruitmentProfitInfo();
    }
  });

  // Debug logging for troubleshooting
  console.log('loadRowData completed:', {
    platformType: props.platformType,
    orderMethod: props.orderMethod,
    showDouyinOrderInfo: showDouyinOrderInfo.value,
    isDouyinRecruitmentTask: isDouyinRecruitmentTask.value,
    originalData: {
      order_info: props.rowData.order_info,
      expect_release_time: props.rowData.order_info?.expect_release_time,
      expect_save_days: props.rowData.order_info?.expect_save_days,
      component_type: props.rowData.order_info?.component_type,
      // Add recruitment specific fields for debugging
      kol_fans_num: props.rowData.kol_fans_num,
      is_xingxiao_kol: props.rowData.is_xingxiao_kol,
      kol_licensing_fee: props.rowData.kol_licensing_fee,
      change_status: props.rowData.change_status,
      customer_rebate: props.rowData.customer_rebate,
      // Add ext object data for debugging
      ext: props.rowData.ext,
      ext_kol_fans_num: props.rowData.ext?.kol_fans_num,
      ext_is_xingxiao_kol: props.rowData.ext?.is_xingxiao_kol,
      ext_customer_rebate_ratio: props.rowData.ext?.customer_rebate_ratio,
      ext_show_customer_fee: props.rowData.ext?.show_customer_fee,
      ext_provider_price: props.rowData.ext?.provider_price,
      // Add process_info _v1 fields for debugging
      process_info: props.rowData.process_info,
      process_info_v1_fields: {
        predict_receivable_customer_price_v1: props.rowData.process_info?.predict_receivable_customer_price_v1,
        gross_profit_v1: props.rowData.process_info?.gross_profit_v1,
        gross_profit_margin_v1: props.rowData.process_info?.gross_profit_margin_v1
      }
    },
    kolForm_basic: {
      kol_name: kolForm.value.kol_name,
      platform_uid: kolForm.value.platform_uid,
      cooperation_type: kolForm.value.cooperation_type,
      kol_price: kolForm.value.kol_price
    },
    kolForm_recruitment: {
      kol_fans_num: kolForm.value.kol_fans_num,
      is_xingxiao_kol: kolForm.value.is_xingxiao_kol,
      kol_licensing_fee: kolForm.value.kol_licensing_fee,
      change_status: kolForm.value.change_status,
      customer_rebate: kolForm.value.customer_rebate,
      customer_service_price: kolForm.value.customer_service_price,
      predict_receivable_medium_ratio: kolForm.value.predict_receivable_medium_ratio,
      expect_release_time: kolForm.value.expect_release_time,
      // Add _v1 fields for debugging
      predict_receivable_customer_price_v1: kolForm.value.predict_receivable_customer_price_v1,
      gross_profit_v1: kolForm.value.gross_profit_v1,
      gross_profit_margin_v1: kolForm.value.gross_profit_margin_v1
    },
    kolForm_process: {
      kol_base_price: kolForm.value.process_info.kol_base_price,
      actual_amount_price: kolForm.value.process_info.actual_amount_price,
      gross_profit: kolForm.value.process_info.gross_profit,
      kol_licensing_fee: kolForm.value.process_info.kol_licensing_fee,
      change_status: kolForm.value.process_info.change_status,
      customer_rebate: kolForm.value.process_info.customer_rebate
    },
    kolForm_order: {
      expect_release_time: kolForm.value.order_info.expect_release_time,
      expect_save_days: kolForm.value.order_info.expect_save_days,
      component_type: kolForm.value.order_info.component_type,
      component_content: kolForm.value.order_info.component_content,
      is_publish_adv: kolForm.value.order_info.is_publish_adv,
      selectedAdvOptions: selectedAdvOptions.value
    }
  });

  // Load recruitment profit info at the very end to ensure it's not overwritten
  loadRecruitmentProfitInfo();
};

// Submit form
const submitForm = async () => {
  if (!kolFormRef.value) return;
  
  try {
    if (isXiaohongshu.value || isTencentMutual.value) {
      kolForm.value.kol_price = kolForm.value.process_info.kol_base_price;
    }
    
    await kolFormRef.value.validate();
    
    let orderInfoData = {};
    
    if (props.orderMethod !== 1 || isXiaohongshu.value || isTencentMutual.value || isKuaishou.value || isBilibili.value) {
      if (isKuaishou.value) {
        orderInfoData = {
          expect_release_time: kolForm.value.order_info.expect_release_time,
          expect_save_days: kolForm.value.order_info.expect_save_days,
          is_publish_adv: kolForm.value.order_info.is_publish_adv || '',
          dou_adv_id: kolForm.value.order_info.dou_adv_id,
          dou_adv_uid: kolForm.value.order_info.dou_adv_uid,
          component_type: kolForm.value.order_info.component_type,
          component_content: kolForm.value.order_info.component_content,
          component_url: kolForm.value.order_info.component_url,
          component_remark: kolForm.value.order_info.component_remark,
          extra_remark: kolForm.value.order_info.extra_remark,
        };
      } else if (showDouyinOrderInfo.value) {
        // For Douyin offline orders, keep data directly in order_info
        orderInfoData = {
          expect_release_time: kolForm.value.order_info.expect_release_time,
          expect_save_days: kolForm.value.order_info.expect_save_days,
          is_publish_adv: kolForm.value.order_info.is_publish_adv || '',
          juliang_effect_adv_id: kolForm.value.order_info.juliang_effect_adv_id,
          juliang_brand_adv_id: kolForm.value.order_info.juliang_brand_adv_id,
          juliang_qianchuan_adv_id: kolForm.value.order_info.juliang_qianchuan_adv_id,
          dou_adv_id: kolForm.value.order_info.dou_adv_id,
          dou_adv_uid: kolForm.value.order_info.dou_adv_uid,
          component_type: kolForm.value.order_info.component_type,
          component_content: kolForm.value.order_info.component_content,
          component_url: kolForm.value.order_info.component_url,
          component_remark: kolForm.value.order_info.component_remark,
          extra_remark: kolForm.value.order_info.extra_remark,
          xingtu_project_name: kolForm.value.order_info.xingtu_project_name
        };
      } else if (isBilibili.value) {
        orderInfoData = {
          expect_release_time: kolForm.value.order_info.expect_release_time,
          ext: { ...kolForm.value.order_info.ext }
        };
      } else {
        orderInfoData = {
          ext: { ...kolForm.value.order_info.ext }
        };
      }
    }
    
    let formData = {};

    if (isDouyinRecruitmentTask.value) {
      // 抖音招募任务：将所有基础信息和利润信息都放在根级别
      formData = {
        // 基础信息
        kol_name: kolForm.value.kol_name,
        platform_uid: kolForm.value.platform_uid,
        cooperation_type: 1, // 招募任务固定为1
        xingtu_task_name: kolForm.value.xingtu_task_name,
        xingtu_project_name: kolForm.value.xingtu_project_name,
        kol_price: Number(kolForm.value.kol_price),
        alliance_personnel: kolForm.value.alliance_personnel,
        alliance_personnel_id: kolForm.value.alliance_personnel_id,
        contact_information: kolForm.value.contact_information,
        kol_num: kolForm.value.kol_num,
        price_1_20: Number(kolForm.value.price_1_20) || 0,
        price_20_60: Number(kolForm.value.price_20_60) || 0,
        price_60: Number(kolForm.value.price_60) || 0,
        rebate_ratio: Number(kolForm.value.rebate_ratio) || 0,
        kol_attributes: kolForm.value.kol_attributes,
        kol_type: kolForm.value.kol_type,
        collection_status: kolForm.value.collection_status,

        // MCN相关信息
        mcnObject: kolForm.value.mcnObject ? JSON.parse(JSON.stringify(kolForm.value.mcnObject)) : {
          mcn_name: "",
          mcn_short_name: "",
          mcn_id: ""
        },
        mcn_name: kolForm.value.mcn_name || "",
        mcn_short_name: kolForm.value.mcn_short_name || "",
        mcn_id: kolForm.value.mcn_id || "",

        // 招募任务特定字段（放在根级别）
        is_xingxiao_kol: kolForm.value.is_xingxiao_kol || '',
        kol_fans_num: kolForm.value.kol_fans_num || '',
        show_customer_fee: Number(kolForm.value.show_customer_fee) || 0,
        customer_rebate_ratio: Number(kolForm.value.customer_rebate_ratio) || 0,
        provider_price_exclue_service_fee: Number(kolForm.value.provider_price_exclue_service_fee) || 0,
        provider_price: Number(kolForm.value.provider_price) || 0,

        // 招募任务利润信息字段（放在根级别，从_v1字段获取值但提交时使用不带_v1的字段名）
        kol_licensing_fee: Number(kolForm.value.kol_licensing_fee) || 0,
        predict_receivable_customer_price: Number(kolForm.value.predict_receivable_customer_price_v1) || 0,
        gross_profit: Number(kolForm.value.gross_profit_v1) || 0,
        gross_profit_margin: Number(kolForm.value.gross_profit_margin_v1) || 0,
        change_status: Number(kolForm.value.change_status) || 0, // 转换为数字值0/1
        customer_rebate: Number(kolForm.value.customer_rebate) || 0,
        customer_service_price: Number(kolForm.value.customer_service_price) || 0,
        predict_receivable_medium_ratio: Number(kolForm.value.predict_receivable_medium_ratio) || 0,
        expect_release_time: kolForm.value.expect_release_time || '',

        // 返点信息
        this_rebate_ratio: Number(kolForm.value.predict_receivable_medium_ratio) || 0,
        this_rebate_price: Number(kolForm.value.process_info.predict_receivable_medium_price) || 0,

        // 其他字段放在process_info中
        process_info: {
          ...kolForm.value.process_info,
          kol_base_price: Number(kolForm.value.kol_price) || 0,
          kol_licensing_fee: Number(kolForm.value.kol_licensing_fee) || 0,
          change_status: Number(kolForm.value.change_status) || 0, // 转换为数字值0/1
          actual_amount_price: Number(kolForm.value.process_info.actual_amount_price) || 0,
          predict_receivable_medium_price: Number(kolForm.value.process_info.predict_receivable_medium_price) || 0,
          predict_receivable_medium_ratio: Number(kolForm.value.predict_receivable_medium_ratio) || 0,
          customer_rebate: Number(kolForm.value.customer_rebate) || 0,
          customer_service_price: Number(kolForm.value.customer_service_price) || 0,
          predict_receivable_customer_price: Number(kolForm.value.predict_receivable_customer_price_v1) || 0,
          gross_profit: Number(kolForm.value.gross_profit_v1) || 0,
          gross_profit_margin: Number(kolForm.value.gross_profit_margin_v1) || 0,
          predict_cost: Number(predict_cost.value) || 0,
          star_price: Number(star_price.value) || 0,
          original_rebate_ratio: Number(original_rebate_ratio.value) || 0,
          expect_release_time: kolForm.value.expect_release_time || ''
        }
      };
    } else {
      // 非招募任务：保持原有的数据结构
      formData = {
        kol_name: kolForm.value.kol_name,
        platform_uid: kolForm.value.platform_uid,
        cooperation_type: kolForm.value.cooperation_type,
        xingtu_task_name: kolForm.value.xingtu_task_name,
        xingtu_project_name: kolForm.value.xingtu_project_name,
        kol_price: Number(kolForm.value.kol_price),
        alliance_personnel: kolForm.value.alliance_personnel,
        alliance_personnel_id: kolForm.value.alliance_personnel_id,
        contact_information: kolForm.value.contact_information,
        kol_num: kolForm.value.kol_num,
        price_1_20: Number(kolForm.value.price_1_20) || 0,
        price_20_60: Number(kolForm.value.price_20_60) || 0,
        price_60: Number(kolForm.value.price_60) || 0,
        rebate_ratio: Number(kolForm.value.rebate_ratio) || 0,
        kol_attributes: kolForm.value.kol_attributes,
        kol_type: kolForm.value.kol_type,
        collection_status: kolForm.value.collection_status,

        // 明确处理MCN相关信息，确保完整传递到父组件
        mcnObject: kolForm.value.mcnObject ? JSON.parse(JSON.stringify(kolForm.value.mcnObject)) : {
          mcn_name: "",
          mcn_short_name: "",
          mcn_id: ""
        },
        mcn_name: kolForm.value.mcn_name || "",
        mcn_short_name: kolForm.value.mcn_short_name || "",
        mcn_id: kolForm.value.mcn_id || "",

        this_rebate_ratio: Number(kolForm.value.process_info.predict_receivable_medium_ratio) || 0,
        this_rebate_price: Number(kolForm.value.process_info.predict_receivable_medium_price) || 0,

        process_info: {
          ...kolForm.value.process_info,
          kol_base_price: Number(kolForm.value.kol_price) || 0,
          kol_licensing_fee: Number(kolForm.value.process_info.kol_licensing_fee) || 0,
          change_status: Number(kolForm.value.process_info.change_status) || 0,
          actual_amount_price: Number(kolForm.value.process_info.actual_amount_price) || 0,
          predict_receivable_medium_price: Number(kolForm.value.process_info.predict_receivable_medium_price) || 0,
          predict_receivable_medium_ratio: Number(kolForm.value.process_info.predict_receivable_medium_ratio) || 0,
          customer_rebate: Number(kolForm.value.process_info.customer_rebate) || 0,
          customer_service_price: Number(kolForm.value.process_info.customer_service_price) || 0,
          gross_profit: Number(gross_profit.value) || 0,
          gross_profit_margin: Number(gross_profit_margin.value) || 0,
          predict_cost: Number(predict_cost.value) || 0,
          predict_receivable_customer_price: Number(predict_receivable_customer_price.value) || 0,
          star_price: Number(star_price.value) || 0,
          original_rebate_ratio: Number(original_rebate_ratio.value) || 0
        },

        order_info: props.orderMethod !== 1 || isXiaohongshu.value || isTencentMutual.value || isKuaishou.value || isBilibili.value
          ? orderInfoData
          : undefined
      };
    }
    
    if (kolForm.value.order_info && kolForm.value.order_info.is_publish_adv !== undefined) {
      formData.is_publish_adv = kolForm.value.order_info.is_publish_adv || '';
    }

    // 调试日志：检查提交的数据结构
    console.log('=== 提交表单数据 ===');
    console.log('isDouyinRecruitmentTask:', isDouyinRecruitmentTask.value);
    console.log('表单中的_v1字段值:', {
      predict_receivable_customer_price_v1: kolForm.value.predict_receivable_customer_price_v1,
      gross_profit_v1: kolForm.value.gross_profit_v1,
      gross_profit_margin_v1: kolForm.value.gross_profit_margin_v1
    });

    if (isDouyinRecruitmentTask.value) {
      console.log('招募任务利润信息字段提交值:', {
        predict_receivable_customer_price: formData.predict_receivable_customer_price,
        gross_profit: formData.gross_profit,
        gross_profit_margin: formData.gross_profit_margin,
        kol_licensing_fee: formData.kol_licensing_fee,
        customer_rebate: formData.customer_rebate,
        customer_service_price: formData.customer_service_price
      });
      console.log('process_info中的利润信息:', {
        predict_receivable_customer_price: formData.process_info.predict_receivable_customer_price,
        gross_profit: formData.process_info.gross_profit,
        gross_profit_margin: formData.process_info.gross_profit_margin
      });
    }

    console.log('完整提交数据:', formData);

    emit('confirm', formData);
    
    emit('update:dialogVisible', false);
  } catch (error) {
    ElMessage({
      message: '请填写必填项',
      type: 'error'
    });
  }
};

watch(
  () => props.dialogVisible,
  (newValue) => {
    if (newValue) {
      resetForm();

      loadRowData();

      // B站相关数据已在loadRowData()中处理，这里不需要重复处理

      // Ensure recruitment profit info is loaded after dialog opens
      nextTick(() => {
        loadRecruitmentProfitInfo();
      });

      activeTab.value = 'basic';
    }
  }
);

// Watch for kol_price changes to update process_info.kol_base_price
watch(
  () => kolForm.value.kol_price,
  (newValue) => {
    kolForm.value.process_info.kol_base_price = newValue;
    updatePriceBasedOnType();
    updatePlatformTotalPrice();
    
    // If media ratio is already set, update price
    if (Number(kolForm.value.process_info.predict_receivable_medium_ratio) > 0) {
      changeMediumRatio(kolForm.value.process_info.predict_receivable_medium_ratio);
    }
    
    // Synchronize calculated values
    syncCalculatedValues();
  }
);

// Watch for cooperation_type changes to update price
watch(
  () => kolForm.value.cooperation_type,
  (newValue) => {
    // Update kol_price based on selected cooperation type
    if (props.platformType === 1 || props.platformType === 2) {
      // Douyin platform
      if (newValue === 1 && kolForm.value.price_1_20) {
        kolForm.value.kol_price = kolForm.value.price_1_20;
      } else if (newValue === 2 && kolForm.value.price_20_60) {
        kolForm.value.kol_price = kolForm.value.price_20_60;
      } else if (newValue === 71 && kolForm.value.price_60) {
        kolForm.value.kol_price = kolForm.value.price_60;
      }
    } else if (props.platformType === 3) {
      // Xiaohongshu
      if (newValue === 1 && kolForm.value.price_1_20) {
        kolForm.value.kol_price = kolForm.value.price_1_20; // Text note
      } else if (newValue === 2 && kolForm.value.price_20_60) {
        kolForm.value.kol_price = kolForm.value.price_20_60; // Video note
      }
    } else if (props.platformType === 6) {
      // Tencent
      if (newValue === 1 && kolForm.value.price_1_20) {
        kolForm.value.kol_price = kolForm.value.price_1_20; // 1-60s
      } else if (newValue === 2 && kolForm.value.price_60) {
        kolForm.value.kol_price = kolForm.value.price_60; // 60s+
      }
    }
  }
);

// Watch for kol_licensing_fee changes to update actual_amount_price
watch(
  () => kolForm.value.process_info.kol_licensing_fee,
  () => {
    // Only update if not manually adjusting price
    if (kolForm.value.process_info.change_status === 0) {
      kolForm.value.process_info.actual_amount_price = Number(Number(star_price.value).toFixed(2));
    }
    
    // Update platform-specific total prices
    updatePlatformTotalPrice();
    
    // Update dependent calculations
    syncCalculatedValues();
  }
);

// Watch for star_price changes to update actual_amount_price when change_status is 0
watch(
  () => star_price.value,
  (newValue) => {
    // Only update if not manually adjusting price
    if (kolForm.value.process_info.change_status === 0) {
      kolForm.value.process_info.actual_amount_price = Number(Number(newValue).toFixed(2));
    }
    
    // Update dependent calculations
    syncCalculatedValues();
  }
);

// Watch for actual_amount_price changes to update original rebate ratio in real-time
watch(
  () => kolForm.value.process_info.actual_amount_price,
  () => {
    // Only update if price adjustment is enabled
    if (kolForm.value.process_info.change_status === 1) {
      // Update dependent calculations including original rebate ratio
      syncCalculatedValues();
    }
  }
);

// Watch for platform type changes to update default tab and validate fields
watch(
  () => props.platformType,
  () => {
    /* Commented out to prevent auto-switching tabs
    // Set appropriate active tab
    if (newValue === 3) {
      activeTab.value = 'xiaohongshu';
    } else if (newValue === 6) {
      activeTab.value = 'tencent';
    } else if (showDouyinOrderInfo.value) {
      activeTab.value = 'douyin';
    } else {
      // Default to profit tab for other cases
      activeTab.value = 'profit';
    }
    */

    // Update platform-specific calculations
    updatePlatformTotalPrice();
  }
);

// Watch for Bilibili service type changes to update star_price
watch(
  () => kolForm.value.process_info.bili_service_type,
  () => {
    // Only update if not manually adjusting price
    if (kolForm.value.process_info.change_status === 0) {
      nextTick(() => {
        kolForm.value.process_info.actual_amount_price = Number(Number(star_price.value).toFixed(2));
        syncCalculatedValues();
      });
    }
  }
);

// Initialize component
onMounted(() => {
  if (props.dialogVisible && props.rowData && Object.keys(props.rowData).length > 0) {

    loadRowData();

    // Ensure recruitment profit info is loaded on mount
    nextTick(() => {
      loadRecruitmentProfitInfo();
    });

    let advValue = '';
    
    // Check all possible sources for is_publish_adv with explicit debugging
    // PRIORITY ORDER: 1. Root level, 2. order_info, 3. order_info.ext
    if (props.rowData.is_publish_adv) {
      advValue = props.rowData.is_publish_adv;
    } else if (props.rowData.order_info && props.rowData.order_info.is_publish_adv) {
      advValue = props.rowData.order_info.is_publish_adv;
    } else if (props.rowData.order_info && props.rowData.order_info.ext && props.rowData.order_info.ext.is_publish_adv) {
      advValue = props.rowData.order_info.ext.is_publish_adv;
    }
    
    if (advValue) {
      
      // Ensure the value is stored in the correct location
      kolForm.value.order_info.is_publish_adv = advValue;
      
      // Set the appropriate checkbox group based on platform type
      if (isKuaishou.value) {
        kuaishouAdvOptions.value = advValue.split(',').map(String);
      } else {
        selectedAdvOptions.value = advValue.split(',').map(String);
      }
      
    }
  }
});

// Add handleMediumChange function
const handleMediumChange = (value) => {
  if (!value) return;
  
  // 根据选择的名称查找对应的媒介对象
  const selectedMedium = props.mediumOptions.find(item => item.name === value);
  if (selectedMedium) {
    // 同步更新alliance_personnel_id
    kolForm.value.alliance_personnel_id = selectedMedium.id;
  }
};

// Handle base price change in profit tab
const handleBasePriceChange = (value) => {
  // Update kol_price based on process_info.kol_base_price
  kolForm.value.kol_price = value;
  
  // Update price based on collaboration type
  updatePriceBasedOnType();
  
  // Update media rebate price if ratio is set
  if (Number(kolForm.value.process_info.predict_receivable_medium_ratio) > 0) {
    changeMediumRatio(kolForm.value.process_info.predict_receivable_medium_ratio);
  }
  
  // Update platform-specific total price calculations
  updatePlatformTotalPrice();
  
  // Synchronize calculated values
  syncCalculatedValues();
};

// 在script部分的methods处添加formatActualAmount方法
const formatActualAmount = () => {
  if (kolForm.value.process_info.actual_amount_price === '' || kolForm.value.process_info.actual_amount_price === null) {
    return;
  }
  kolForm.value.process_info.actual_amount_price = Number(Number(kolForm.value.process_info.actual_amount_price).toFixed(2));
  
  // When there's a price adjustment, update dependent calculations including original rebate ratio
  if (kolForm.value.process_info.change_status === 1) {
    // Update all calculated values to ensure original rebate ratio is updated
    syncCalculatedValues();
  }
};

// Handle Kuaishou advertising options change - UPDATED
const handleKuaishouAdvOptionsChange = (value) => {
  // Make sure we're working with string values
  const stringValues = value.map(String);
  
  // Get previous values to compare with new selection
  const previousValues = kolForm.value.order_info.is_publish_adv ? 
    kolForm.value.order_info.is_publish_adv.split(',') : [];
    
  // Check which options were removed
  const removedOptions = previousValues.filter(opt => !stringValues.includes(opt));
  
  // Clear corresponding fields when options are deselected
  if (removedOptions.includes('1')) {
    kolForm.value.order_info.dou_adv_id = '';
  }
  
  if (removedOptions.includes('2')) {
    kolForm.value.order_info.dou_adv_uid = '';
  }
  
  const isPublishAdv = stringValues.join(',');
  kolForm.value.order_info.is_publish_adv = isPublishAdv;
  
  nextTick(() => {
    if (kolFormRef.value) {
      kolFormRef.value.validateField([
        'order_info.dou_adv_id',
        'order_info.dou_adv_uid'
      ]);
    }
  });
};


</script>

<style scoped>
.unified-kol-dialog :deep(.el-dialog__body) {
  padding: 10px 20px;
}

.unified-kol-dialog .dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  height: 50px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
}

.unified-kol-dialog .dialog-header .title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.unified-kol-dialog .dialog-header .el-icon {
  cursor: pointer;
  color: #909399;
}

.unified-kol-dialog .dialog-header .el-icon:hover {
  color: #409eff;
}

.unified-kol-dialog .dialog-footer {
  display: flex;
  justify-content: flex-end;
  padding: 10px 20px;
  border-top: 1px solid #e4e7ed;
}

.unified-kol-dialog .form-container {
  max-height: 65vh;
  overflow-y: auto;
  padding: 10px 0;
}

.unified-kol-dialog .basic-info-section {
  margin-bottom: 20px;
}

.unified-kol-dialog .el-form .el-form-item {
  margin-bottom: 18px;
}

.unified-kol-dialog .el-tabs {
  margin-top: 15px;
}

.unified-kol-dialog .profit-info-section .section-title,
.unified-kol-dialog .form-section .section-title {
  font-weight: bold;
  margin-bottom: 10px;
  color: #606266;
}

.unified-kol-dialog .profit-info-section .calculation-formula {
  font-size: 12px;
  color: #909399;
  margin-left: 5px;
}

.unified-kol-dialog .flex-row {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;
}

.unified-kol-dialog .flex-row .form-item-group {
  display: flex;
  align-items: center;
  margin-right: 20px;
}

.unified-kol-dialog .upload-demo {
  margin-top: 10px;
}

.unified-kol-dialog .read-only-field {
  background-color: #f5f7fa;
  cursor: not-allowed;
}

.unified-kol-dialog .info-tooltip {
  margin-left: 5px;
  cursor: help;
}

.unified-kol-dialog .special-remarks {
  margin-top: 15px;
}

.unified-kol-dialog .special-remarks .el-textarea {
  width: 100%;
}

/* Responsive adjustments for small screens */
@media (max-width: 768px) {
  .unified-kol-dialog .el-form-item {
    width: 100%;
  }
}

/* Dialog width overrides */
:deep(.el-dialog) {
  width: 80%;
  max-width: 1200px;
}

:deep(.el-select) {
  width: 100%;
}

:deep(.el-input-number) {
  width: 100%;
}

/* Image preview dialog */
.preview-image {
  width: 100%;
  max-height: 80vh;
  object-fit: contain;
}

.col-start {
  margin-left: 0;
}
</style> 
